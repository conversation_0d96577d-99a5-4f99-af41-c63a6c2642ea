package dto

type JwtCheckRequest struct {
	ClientID string `json:"client_id"`
}

type JwtCheckResponse struct {
	OrgName  string `json:"org_name"`
	ClientID string `json:"client_id"`
	UserID   string `json:"uid"`
	UserName string `json:"user_name"`
	Channel  string `json:"channel"`
}

type JwtBaseReceiveResponse struct {
	Error   int    `json:"error"`
	Message string `json:"message"`
	Data    any    `json:"data"`
}
