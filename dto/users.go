package dto

import "time"

const (
	TypeOfPhone = 0
	TypeOfEmail = 1
	TypeOfLdap  = 2
)

type Users struct {
	UserID    string `json:"user_id" bson:"user_id"`
	CreatedAt int64  `json:"created_at" bson:"created_at"`
	UpdatedAt int64  `json:"updated_at" bson:"updated_at"`
	Username  string `json:"username" bson:"username"`
	ProductID string `json:"product_id" bson:"product_id"`
}

type UserLogin struct {
	UserID string `json:"user_id" bson:"user_id"`

	ProductID   string `json:"product_id" bson:"product_id"`
	LoginValue  string `json:"login_value" bson:"login_value"` // 手机号/邮箱
	LoginType   int8   `json:"login_type" bson:"login_type"`   //0:手机号 1:邮箱 2:账号
	SourceType  string `json:"source_type" bson:"source_type"`
	CreatedAt   int64  `json:"created_at" bson:"created_at"`
	UpdatedAt   int64  `json:"updated_at" bson:"updated_at"`
	State       int32  `json:"state" bson:"state"`
	LoginMethod string `json:"login_method" bson:"login_method"` // email_login phone_login ldap_login   // 用户的登陆方式
}

type UserTokenData struct {
	UserID    string `json:"user_id" bson:"user_id"`
	ProductID string `json:"product_id" bson:"product_id"`
	ClientID  string `json:"client_id" bson:"client_id"`
	Token     string `json:"token" bson:"token"`

	ExpiredAt time.Time `json:"expired_at" bson:"expired_at"`
	CreatedAt int64     `json:"created_at" bson:"created_at"`
	UpdatedAt int64     `json:"updated_at" bson:"updated_at"`
}

type UserLoginRecordStruct struct {
	Phone       string `json:"phone"`
	Account     string `json:"account"`
	Email       string `json:"email"`
	ProductID   string `json:"product_id"`
	ClientID    string `json:"client_id"`
	Token       string `json:"token"`
	ExpiatedAt  int    `json:"expiated_at"`
	LoginMethod string `json:"login_method" bson:"login_method"` // email_login phone_login ldap_login
}

type ThirdPartyRequest struct {
	Provider string
	OrgCode  string
	Code     string
	Ticket   string
	ClientID string
}

type SearchUserInfoRequest struct {
	ProductID string `json:"product_id"`
	Phone     string `json:"phone"`
	Email     string `json:"email"`
	Account   string `json:"account"`
	UserID    string `json:"user_id"`
}
