package dto

type UserAgreement struct {
	UserCode    string `json:"user_code" bson:"user_code"`
	ProductId   string `json:"product_id" bson:"product_id"`
	UserID      string `json:"user_id" bson:"user_id"`
	AgreementID string `json:"agreement_id" bson:"agreement_id"`
	CreatedAt   int64  `json:"created_at" bson:"created_at"`
	UpdatedAt   int64  `json:"updated_at" bson:"updated_at"`
	ClientID    string `json:"client_id" bson:"client_id"` // 分支的数据
	Checked     int    `json:"checked" bson:"checked"`     // 0:未勾选 1:勾选
}
