package dto

import "encoding/json"

type EnterpriseSetting struct {
	ProductID string `json:"product_id" bson:"product_id"`
	Contents  string `json:"contents" bson:"contents"`
	Type      string `json:"type" bson:"type"`
}

type AccountSetting struct {
	BruteForceProtection struct {
		IPLoginSwitch      bool `json:"ip_login_switch" bson:"ip_login_switch"`
		AccountLoginSwitch bool `json:"account_login_switch" bson:"account_login_switch"`
		IPLoginConfig      struct {
			Duration           int `json:"duration" bson:"duration"` // 当ip_login_switch开启时，每 duration分钟内 ，同一Ip登录失败failed_num次，禁止该IP请求prohibit_ip_duration 分钟
			FailedNum          int `json:"failed_num" bson:"failed_num"`
			ProhibitIPDuration int `json:"prohibit_login_duration" bson:"prohibit_login_duration"`
		} `json:"ip_login_config" bson:"ip_login_config"`
		AccountLoginConfig struct {
			Duration              int `json:"duration" bson:"duration"` // 当account_login_switch开启时，每 duration分钟内 ，同一账号登录失败failed_num次，禁止该账号请求prohibit_ip_duration 分钟
			FailedNum             int `json:"failed_num" bson:"failed_num"`
			ProhibitLoginDuration int `json:"prohibit_login_duration" bson:"prohibit_login_duration"`
		} `json:"account_login_config" bson:"account_login_config"`
	} `json:"brute_force_protection" bson:"brute_force_protection"`
}

type JoinCompanySetting struct {
	OpenEnterprisePwd bool   `json:"open_enterprise_pwd" bson:"open_enterprise_pwd"`
	Pwd               string `json:"pwd" bson:"pwd"`
}

func (e *EnterpriseSetting) GetAccountSetting() *AccountSetting {
	if e == nil {
		return nil
	}
	if e.Contents == "" {
		return nil
	}
	settings := &AccountSetting{}
	_ = json.Unmarshal([]byte(e.Contents), &settings)

	return settings
}

type LoginSetting struct {
	LoginType string `json:"login_type" bson:"login_type"` // force normal
}

func (e *EnterpriseSetting) GetLoginSetting() *LoginSetting {
	if e == nil {
		return nil
	}
	if e.Contents == "" {
		return nil
	}

	settings := &LoginSetting{}
	_ = json.Unmarshal([]byte(e.Contents), &settings)

	return settings

}

func (e *EnterpriseSetting) GetJoinCompanySetting() *JoinCompanySetting {
	if e == nil {
		return nil
	}
	if e.Contents == "" {
		return nil
	}
	settings := &JoinCompanySetting{}
	_ = json.Unmarshal([]byte(e.Contents), &settings)

	return settings
}

type EnterpriseLoginTypeRequest struct {
	ProductID string `json:"product_id" validate:"required"`
}

type EnterpriseLoginResponse struct {
	Items          []*EnterpriseLoginSetting `json:"items"`
	ProductID      string                    `json:"product_id"`
	EnterpriseName string                    `json:"enterprise_name" bson:"enterprise_name"`
	EnterpriseLogo string                    `json:"enterprise_logo" bson:"enterprise_logo"`
}

type EnterpriseInfo struct {
	EnterpriseName string `json:"enterprise_name" bson:"enterprise_name"`
	EnterpriseLogo string `json:"enterprise_logo" bson:"enterprise_logo"`
}

func (e *EnterpriseSetting) GetEnterpriseInfo() *EnterpriseInfo {
	if e == nil {
		return nil
	}
	if e.Contents == "" {
		return nil
	}
	settings := &EnterpriseInfo{}
	_ = json.Unmarshal([]byte(e.Contents), &settings)

	return settings
}

func (e *EnterpriseInfo) GetEnterpriseName() string {

	if e == nil {
		return ""
	}
	return e.EnterpriseName
}

func (e *EnterpriseInfo) GetEnterpriseLogo() string {
	if e == nil {
		return ""
	}

	return e.EnterpriseLogo
}
