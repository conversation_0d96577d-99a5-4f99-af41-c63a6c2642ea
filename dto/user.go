package dto

type UserGroupRelation struct {
	GroupCode string `json:"group_code" bson:"group_code"`
}

type UserDepartmentRelation struct {
	DeptPath [][]string `json:"dept_path" bson:"dept_path"`
}

type PolicyAppRelation struct {
	PolicyCode string `json:"policy_code" bson:"policy_code"`
}

type AppPolicy struct {
	AppCodes []string `json:"app_codes" bson:"app_codes"`
	AppTags  []string `json:"app_tags" bson:"app_tags"`
}

type BindHostInfoDataRequest struct {
	ClientId string `json:"client_id"`
	UserCode string
	UserName string
	OrgName  string
	Token    string
}
