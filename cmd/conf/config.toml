[service]
name = 'ztna_server_auth'
mode = 'dev'
version = '1.0.0.0'
addr = "0.0.0.0:8081"
is_test_mode = true
# 用户离线检查间隔时间（分钟），超过此时间没有活动的用户将被标记为离线
offline_check_interval = 30

[logging]
type = 'text'
dir = 'log'
level = 'info'
stdout = true

[redis_default]
connection_mode = 0
addr = ['***************:36379']
password = ''
db = 0

[mongodb_rm_sase]
resource_prefix = 'alpha-'
addr = "mongodb.outside.rongma.tech:27017"
username = "alpha-rmclient"
password = "zo3RdOgGio*F"
dbname = "rm_sase"
direct = true
options = ""


[jwt]
secret_key = "your-secure-jwt-secret-key-should-be-very-long"
access_expiry = 7200 # 2小时（秒）
refresh_expiry = 2592000 # 30天（秒）
issuer = "ztna_auth_service"


[sms]
secret_id = 'AKIDqaCcqpaBYfYAZjfGvPjrkPr6B0Zv53nK'
secret_key = 'NVSMEqCI8h9QYWFjxwf7KG84qPkgMnzu'
sms_sdk_appid = '1400789505'
captcha_appid = 193511179
captcha_app_secret_key = 'MhZw9KSblVv6JVtDubE0ImQGE'
sign_name = '戎码科技'
# 同一个手机号验证码两次发送时间间隔（秒）
verification_code_interval = 120
# 每个手机号验证码有效时间（分钟）
verification_code_valid_time = 5
# 【不替换生产的】同一个手机号验证码每天允许的最大发送次数，0点更新
verification_code_times_day_for_phone = 30
# 【不替换生产的】同一个IP验证码每天允许的最大发送次数，0点更新
verification_code_times_day_for_ip = 100

[email]
port = 465
host = "smtpdm.aliyun.com"
user = "<EMAIL>"
password = "abcU7vOqKRq3"
alias_name = "戎码科技"
# 邮箱验证码模板
verification_subject = "【戎码科技】验证码"
verification_template = "%s是您的验证码，有效期%d分钟，您正在注册（或您正登录），请勿将验证码转发给他人，验证码泄露可能导致账号被盗用。"

[third_party]
address = "http://***************:10002"
#log_tracker_address = "http://***************:16003"
ip_query_url = "https://geo.rongma.com/api/v1/geo_ip"
console_url= "https://alpha-sase-test.rongma.tech/"
log_tracker_address = "https://alpha-sase-app.rongma.tech"


#address = "http://sase-org-sync-service.sase-app-service:80"

[grpc_info]
# 是否验证证书
is_cheack_cert = true
addr = 'alpha-saas-grpc.rongma.tech:8443'

[certs]
rsa_2048 = 'certs/rsa2048.prvkey'
rsa_4096 = 'certs/rsa4096.prvkey'
crt = "certs/client.crt"
key = "certs/client.key"
ca = "certs/ca.crt"
qax_grpc = 'certs/qianxin.crt'



[email_subject_conf.zh]
subject = "奇安云镜验证码登录"
product_name = "奇安云镜"
alias_name = "奇安云镜SASE"

[email_subject_conf.en]
subject = "[Cloud Mirror] Validate Code"
product_name = "Cloud Mirror"
alias_name = "Cloud Mirror SASE System"


[passport]
appid = 47
app_secret = "f1160b82e8836b682dcfd117888eb6fe"
callback = "https://alpha-sase-test.rongma.tech"
passport_backend_url = "http://***************:8087"
