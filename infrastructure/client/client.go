// Package client init middlware db kafka
package client

import (
	"context"
	"fmt"
	"os"

	"google.golang.org/grpc"

	"ztna_server_auth/common/config"
	"ztna_server_auth/common/logs"

	"rm.git/client_api/rm_common_libs.git/v2/common/clients"

	rmCommonInterfaces "rm.git/client_api/rm_common_libs.git/v2/interfaces"
	infoGrpc "rm.git/cloud_api/rm_common_protos.git/proto_go/info_v1"
)

var (
	Redis          rmCommonInterfaces.RedisClient
	Mongo          *clients.MongoDB
	groupConn      *grpc.ClientConn
	InfoGrpcClient infoGrpc.InfoClient
)

func Init(conf *config.AppConfig) {
	var err error
	logs.Infof("[初始化] - Redis: %+v", conf.Redis)

	if Redis, err = clients.NewRedis(conf.Redis); err != nil {
		fmt.Println("\033[5;31m", "阻断错误信息：", err, "\033[0m")
		os.Exit(-1)
	}

	logs.Infof("[初始化] - MongoDB: %+v", conf.Mongo)
	if Mongo, err = clients.NewMongodb(conf.Mongo, conf.Mongo.DBName); err != nil {
		fmt.Println("\033[5;31m", "阻断错误信息：", err, "\033[0m")
		os.Exit(-1)
	}
	logs.Infof("[初始化] - grpc CloudInfo: %+v", conf.GrpcCloudInfo)
	if groupConn, err = clients.NewGrpc(conf.Certs, conf.GrpcCloudInfo, "rm_self"); err != nil {
		fmt.Println("\033[5;31m", "阻断错误信息：", err, "\033[0m")
		os.Exit(-1)
	} else {
		InfoGrpcClient = infoGrpc.NewInfoClient(groupConn)
	}
}

func Close() error {
	_ = Redis.Close()
	_ = Mongo.Client.Disconnect(context.Background())
	return nil
}
