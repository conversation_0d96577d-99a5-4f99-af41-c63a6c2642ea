// Package infrastructure init test
package infrastructure

import (
	"context"

	"ztna_server_auth/common/config"
	"ztna_server_auth/common/logs"
	"ztna_server_auth/common/utils"
	"ztna_server_auth/dto"
	"ztna_server_auth/infrastructure/client"
)

func Init(conf *config.AppConfig) {
	client.Init(conf)

	initTestUsers(conf)
}

func initTestUsers(conf *config.AppConfig) {
	ctx := context.Background()

	// 添加测试用户
	for _, testUser := range conf.TestUsers.User {
		// 密码加密
		passwordHash, err := utils.HashPassword(testUser.Password)
		if err != nil {
			logs.PErrorf("哈希密码失败: %v", err)
			continue
		}

		// 创建用户信息
		user := dto.UserInfo{
			UserCode:     testUser.UserCode,
			Username:     testUser.Username,
			Email:        testUser.Email,
			Phone:        testUser.Phone,
			OrgName:      testUser.OrgName,
			PasswordHash: passwordHash,
			CreatedAt:    testUser.CreatedAt,
			UpdatedAt:    testUser.UpdatedAt,
		}

		// 转换为JSON
		userJSON, err := utils.JsonMarshal(user)
		if err != nil {
			logs.PErrorf("序列化用户信息失败: %v", err)
			continue
		}

		// 存储到Redis，按用户名和用户ID两种方式存储
		usernameKey := "auth:user:" + user.Username
		userCodeKey := "auth:user:" + user.UserCode

		// 设置永不过期
		err = client.Redis.Set(ctx, usernameKey, userJSON, 0).Err()
		if err != nil {
			logs.PErrorf("将用户存储到Redis失败(用户名): %v", err)
			continue
		}

		err = client.Redis.Set(ctx, userCodeKey, userJSON, 0).Err()
		if err != nil {
			logs.PErrorf("将用户存储到Redis失败(用户ID): %v", err)
			continue
		}

		logs.PInfof("成功添加测试用户: %s (%s)", user.Username, user.UserCode)
	}
}
