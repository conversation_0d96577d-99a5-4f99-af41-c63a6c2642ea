// Package setting  define  enterprise setting
package setting

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"time"

	"ztna_server_auth/common/config"
	"ztna_server_auth/common/consts"
	CError "ztna_server_auth/common/errors"
	"ztna_server_auth/common/logs"
	"ztna_server_auth/common/utils"
	"ztna_server_auth/dto"
	"ztna_server_auth/infrastructure/client"

	"github.com/spf13/cast"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

var conf = config.Config()

type EnterpriseSettingService interface {
	FindEnterpriseSetting(ctx context.Context, productId, Type string) (*dto.EnterpriseSetting, error)
	FindEnterpriseLoginType(ctx context.Context, req *dto.EnterpriseLoginTypeRequest) (*dto.EnterpriseLoginResponse, error)
}
type enterpriseSettingService struct {
	enterpriseSettingCollection *mongo.Collection
}

func NewEnterpriseSettingService() EnterpriseSettingService {
	return &enterpriseSettingService{
		enterpriseSettingCollection: client.Mongo.Database.Collection("enterprise_setting"), // 企业设置表
	}
}

func (e *enterpriseSettingService) FindEnterpriseSetting(ctx context.Context, productId, Type string) (*dto.EnterpriseSetting, error) {
	setting := &dto.EnterpriseSetting{}
	if err := e.enterpriseSettingCollection.FindOne(ctx, bson.M{"product_id": productId, "type": Type}).Decode(&setting); err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}
		logs.Warnf("根据产品id :%s 查询企业设置失败: %v", productId, err)
		return nil, CError.ErrorEnterpriseSetting
	}
	return setting, nil
}

func (e *enterpriseSettingService) FindEnterpriseLoginType(ctx context.Context, req *dto.EnterpriseLoginTypeRequest) (*dto.EnterpriseLoginResponse, error) {

	resp := make([]*dto.EnterpriseLoginSetting, 0, 3)

	resp = append(resp, &dto.EnterpriseLoginSetting{LoginType: consts.EmailLogin})
	resp = append(resp, &dto.EnterpriseLoginSetting{LoginType: consts.PhoneLogin})
	resp = append(resp, &dto.EnterpriseLoginSetting{LoginType: consts.Ldap})

	response := &dto.EnterpriseLoginResponse{
		Items:     resp,
		ProductID: req.ProductID,
	}

	enterpriseInfo, err := getEnterpriseInfo(ctx, req.ProductID)
	if err != nil {
		logs.Warnf("failed to call getOrgnameInfo ,error %s", err)
		//return nil, err
	} else {
		response.EnterpriseName = enterpriseInfo.Data.EnterPriseName
	}

	return response, nil

}

type Response struct {
	Data struct {
		EnterPriseName string `json:"organization"`
		OrgID          any    `json:"org_id"`
	} `json:"data"`
}

func getEnterpriseInfo(ctx context.Context, productID string) (*Response, error) {

	currentTime := cast.ToString(time.Now().Unix())
	appID := conf.Passport.Appid
	appSecret := conf.Passport.AppSecret

	uuID := cast.ToString(time.Now().UnixNano())
	requestData := map[string]interface{}{
		"sign":     utils.GeneratorPassportSign(appID, appSecret, uuID, currentTime),
		"appid":    cast.ToInt(appID),
		"time":     cast.ToInt(currentTime),
		"uuid":     uuID,
		"callback": conf.Passport.Callback,
		"org_name": productID,
	}
	logs.PInfof("requestData is %+v", requestData)
	// 将请求数据转换为JSON
	jsonData, err := json.Marshal(requestData)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request data: %v", err)
	}

	// 创建HTTP请求
	url := fmt.Sprintf("%s/rm_passport/v1/external/get_orginfo_by_orgname", conf.Passport.PassportBackendUrl)
	req, err := http.NewRequest(http.MethodPost, url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %v", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")

	// 发送请求
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %v", err)
	}
	fmt.Println("get response Data", string(body))
	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("request failed with status: %d, body: %s", resp.StatusCode, string(body))
	}

	var response = &Response{}
	err = json.Unmarshal(body, &response)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %v", err)
	}

	return response, nil
}
