// Package auth define logic for authentication service
package auth

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math/rand/v2"
	"net/http"
	"net/url"
	"strings"
	"time"
	"unicode"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"ztna_server_auth/common/config"
	"ztna_server_auth/common/consts"
	commondto "ztna_server_auth/common/dto"
	CError "ztna_server_auth/common/errors"
	"ztna_server_auth/common/logs"
	"ztna_server_auth/common/utils"
	"ztna_server_auth/dto"
	"ztna_server_auth/infrastructure/client"
	"ztna_server_auth/service/agreement"
	"ztna_server_auth/service/portal"
	"ztna_server_auth/service/setting"
	"ztna_server_auth/service/user_login"
)

// 相关常量
const (
	AccessTokenExpiry  = 2 * time.Hour
	RefreshTokenExpiry = 30 * 24 * time.Hour
	AccessTokenPrefix  = "auth:access_token:"
	RefreshTokenPrefix = "auth:refresh_token:"
	UserPrefix         = "auth:user:"
	PauseAccessPrefix  = "auth:pause_access:"
)

var conf = config.Config()

// Service 认证服务接口
type Service interface {
	VerificationCodeLoginV2(ctx context.Context, req *dto.VerificationCodeLoginRequest) (*dto.TokenResponse, error)
	RefreshTokenV2(ctx context.Context, refreshToken, clientID string) (*dto.TokenResponse, error)
	// ValidateToken 验证访问令牌
	ValidateToken(ctx context.Context, accessToken string) (*dto.AuthUserInfo, error)
	// GetTokenExpireInfo 获取令牌过期时间
	GetTokenExpireInfo(ctx *gin.Context, req *dto.BindHostInfoDataRequest) (int64, error)

	// SetUserInfoStatusLocked 给用户设置锁定状态
	SetUserInfoStatusLocked(ctx context.Context, req *dto.LoginAccountIP) error
	// LdapDomainAccountLoginV2 ldap 登录
	LdapDomainAccountLoginV2(ctx context.Context, req *dto.LdapDomainAccountLoginRequest) (*dto.TokenResponse, error)
	// ValidateTokenV2 验证token
	ValidateTokenV2(ctx context.Context, accessToken string) (*dto.AuthUserInfo, error)

	ClearToken(ctx context.Context, req *dto.UserTokenData) error
}

type authService struct {
	config                           *config.AppConfig
	organizationCollection           *mongo.Collection
	organizationUserCollection       *mongo.Collection
	departmentCollection             *mongo.Collection
	appCollection                    *mongo.Collection
	userAccountDisposal              *mongo.Collection
	departmentUserRelationCollection *mongo.Collection
	setting                          setting.EnterpriseSettingService
	portal                           portal.Service
	userLogin                        user_login.Service
}

// NewAuthService 创建认证服务实例
func NewAuthService(conf *config.AppConfig) Service {
	return &authService{
		config:                           conf,
		organizationCollection:           client.Mongo.Database.Collection("organizations"),     // 组织机构表
		organizationUserCollection:       client.Mongo.Database.Collection("organization_user"), // 组织用户表
		appCollection:                    client.Mongo.Database.Collection("apps_base"),         // 应用表
		departmentCollection:             client.Mongo.Database.Collection("organization_department"),
		userAccountDisposal:              client.Mongo.Database.Collection("users_account_disposal"),
		departmentUserRelationCollection: client.Mongo.Database.Collection("organization_department_user_relation"),
		setting:                          setting.NewEnterpriseSettingService(),
		portal:                           portal.NewPortalConfig(),
		userLogin:                        user_login.NewUserLogin(),
	}
}

// Login 实现普通账号密码登录
func (s *authService) Login(ctx context.Context, req *dto.GetUserInfoByPhoneOrEmail) (*dto.TokenResponse, error) {
	var user *dto.UserInfo

	filter := bson.M{"status": bson.M{"$ne": dto.UserStatusDelete}}
	if req.ProductID != "" {
		filter["product_id"] = req.ProductID
	}
	if req.OrgCode != "" {
		filter["org_code"] = req.OrgCode
	}

	if req.Account != "" {
		filter["account"] = req.Account
	}
	if req.Phone != "" {
		filter["mobile"] = req.Phone
	}
	if req.Email != "" {
		filter["email"] = req.Email
	}

	// 其他用户的正常验证流程
	if err := s.organizationUserCollection.FindOne(ctx, filter).Decode(&user); err != nil {
		logs.Errorf("用户不存在: %s, error: %v, filter: %v", req.Account, err, filter)
		return nil, CError.ErrorPassword
	}

	if user.Status == dto.UserStatusPause {
		logs.PErrorf("用户已被禁用: %s", user.Username)
		return nil, CError.ErrorUserBlocked
	}
	if user.Status == dto.UserStatusLocked {
		logs.PErrorf("用户已被锁定: %s", user.Username)
		return nil, CError.ErrorUserLocked
	}

	// 验证密码
	if !utils.VerifyPassword(req.Pwd, user.PasswordHash) {
		logs.PErrorf("密码错误: %s", user.Username)
		return nil, CError.ErrorPassword
	}
	org := &dto.Organization{}
	if err := s.organizationCollection.FindOne(ctx, bson.M{"org_code": req.OrgCode, "product_id": req.ProductID}).Decode(&org); err != nil {
		logs.Errorf("根据产品id和orgcode查询组织信息失败: %v", err)
	}
	//  custom organization, force_pwd_change decide to force change password
	if org.SourceConfig != nil && user.PasswordReset == dto.UserPasswordReset && org.SourceType == string(dto.CUSTOM) {
		if !org.SourceConfig.PwdConfig.ForcePwdChange {
			user.PasswordReset = dto.UserPasswordNotReset
		}
	}
	user.OrgName = org.OrgName

	authUserInfo := &dto.AuthUserInfo{
		ProductID:     req.ProductID,
		OrgCode:       req.OrgCode,
		UserCode:      user.UserCode,
		Username:      user.Username,
		Email:         user.Email,
		Phone:         user.Phone,
		OrgName:       org.OrgName,
		PasswordReset: user.PasswordReset,
		ClientID:      req.ClientID,
		LoginMethod:   consts.AccountPassword,
	}
	go s.syncUserLoginTicket(ctx, user.UserCode, user.OrgCode, req.Ticket)

	return s.generateTokens(ctx, authUserInfo)
}

// VerificationCodeLogin 验证码登录（支持手机号和邮箱）
func (s *authService) VerificationCodeLogin(ctx context.Context, req *dto.VerificationCodeLoginRequest) (*dto.TokenResponse, error) {
	// 验证参数：手机号和邮箱不能同时为空，也不能同时有值
	if (req.Phone == "" && req.Email == "") || (req.Email != "" && req.Phone != "") {
		return nil, errors.New("phone and email cannot both be empty or both have values")
	}

	// 构建查询参数
	queryReq := &dto.GetUserInfoByPhoneOrEmail{
		OrgCode:   req.OrgCode,
		ProductID: req.ProductID,
	}

	var loginType, identifier string
	if req.Phone != "" {
		queryReq.Phone = req.Phone
		loginType = consts.PhoneLogin
		identifier = req.Phone
	} else {
		queryReq.Email = req.Email
		loginType = consts.EmailLogin
		identifier = req.Email
	}

	// 根据手机号或邮箱查询用户信息
	userInfo, err := s.getUserInfoByPhoneOrPassword(ctx, queryReq)
	if err != nil {
		logs.Errorf("根据%s查询用户信息失败: %v, %s: %s", loginType, err, loginType, identifier)
		return nil, CError.ErrorUserNotFound
	}

	if userInfo.Status == 1 {
		logs.PErrorf("用户已被禁用: %s", userInfo.Username)
		return nil, CError.ErrorUserBlocked
	}

	// 查询组织信息
	org := &dto.Organization{}
	if err := s.organizationCollection.FindOne(ctx, bson.M{"org_code": userInfo.OrgCode, "product_id": req.ProductID, "status": 1}).Decode(&org); err != nil {
		logs.Errorf("根据产品id和orgcode查询组织信息失败: %v", err)
	}

	// 创建用户信息
	defaultAdmin := &dto.AuthUserInfo{
		OrgCode:       userInfo.OrgCode,
		ProductID:     req.ProductID,
		UserCode:      userInfo.UserCode,
		Username:      userInfo.Username,
		Email:         userInfo.Email,
		Phone:         userInfo.Phone,
		OrgName:       org.OrgName,
		PasswordReset: userInfo.PasswordReset,
		ClientID:      req.ClientID,
		LoginMethod:   loginType,
	}

	// 如果是通过手机号登录，确保Phone字段是传入的手机号
	if req.Phone != "" {
		defaultAdmin.Phone = req.Phone
	}
	// 如果是通过邮箱登录，确保Email字段是传入的邮箱
	if req.Email != "" {
		defaultAdmin.Email = req.Email
	}

	go s.syncUserLoginTicket(ctx, defaultAdmin.UserCode, defaultAdmin.OrgCode, req.Ticket)
	// 直接生成令牌
	return s.generateTokens(ctx, defaultAdmin)
}

// PhoneLogin 手机号验证码登录（保持向后兼容）
func (s *authService) PhoneLogin(ctx context.Context, req *dto.PhoneLoginRequest) (*dto.TokenResponse, error) {
	return s.VerificationCodeLogin(ctx, &dto.VerificationCodeLoginRequest{
		OrgCode:   req.OrgCode,
		ProductID: req.ProductID,
		Phone:     req.Phone,
		Email:     "",
		Code:      req.Code,
		Ticket:    req.Ticket,
		ClientID:  req.ClientID,
	})
}

// RefreshToken 实现刷新令牌
func (s *authService) RefreshToken(ctx context.Context, refreshToken, clientID string) (*dto.TokenResponse, error) {
	// 解析JWT
	claims, err := utils.ParseJWT(refreshToken, s.config.JWT.SecretKey)
	if err != nil {
		logs.PErrorf("解析JWT失败: %v", err)
		return nil, errors.New("invalid refresh token")
	}

	// 确认是刷新令牌而非访问令牌
	if claims.TokenType != "refresh" {
		logs.PErrorf("令牌类型错误，期望refresh token，得到: %s", claims.TokenType)
		return nil, errors.New("invalid token type")
	}

	// 检查刷新令牌是否被撤销
	refreshTokenKey := RefreshTokenPrefix + claims.ID
	_, err = client.Redis.Get(ctx, refreshTokenKey).Result()
	if err != nil {
		logs.PErrorf("刷新令牌已被撤销: %v", err)
		return nil, errors.New("refresh token revoked")
	}

	// 获取用户信息
	user, err := s.getAuthUserInfo(ctx, claims)
	if err != nil {
		logs.PErrorf("获取用户信息失败: %v", err)
		return nil, errors.New("user not found")
	}

	// 撤销旧的刷新令牌
	client.Redis.Del(ctx, refreshTokenKey)
	// 撤销旧的访问令牌(设置过期时间为1分钟)
	accessTokenKey := AccessTokenPrefix + claims.ID
	client.Redis.Expire(ctx, accessTokenKey, time.Minute)
	user.ClientID = clientID
	return s.generateTokens(ctx, user)
}

// ValidateToken 验证访问令牌
func (s *authService) ValidateToken(ctx context.Context, accessToken string) (*dto.AuthUserInfo, error) {
	// 解析JWT
	claims, err := utils.ParseJWT(accessToken, s.config.JWT.SecretKey)
	if err != nil {
		logs.PErrorf("解析JWT失败: %v, accessToken: %s, secretKey: %s", err, accessToken, s.config.JWT.SecretKey)
		return nil, errors.New("invalid access token")
	}

	// 确认是访问令牌而非刷新令牌
	if claims.TokenType != "access" {
		logs.PErrorf("令牌类型错误，期望access token，得到: %s", claims.TokenType)
		return nil, errors.New("invalid token type")
	}
	// 检查令牌是否被撤销
	tokenKey := AccessTokenPrefix + claims.ID
	_, err = client.Redis.Get(ctx, tokenKey).Result()
	if err != nil {
		logs.PErrorf("令牌已被撤销: %v", err)
		return nil, errors.New("token revoked")
	}

	// 获取用户信息
	user, err := s.getAuthUserInfo(ctx, claims)
	if err != nil {
		logs.PErrorf("获取用户信息失败: %v", err)
		return nil, errors.New("user not found")
	}

	return user, nil
}

func (s *authService) GetTokenExpireInfo(ctx *gin.Context, req *dto.BindHostInfoDataRequest) (int64, error) {
	// 解析JWT
	claims, err := utils.ParseJWT(req.Token, s.config.JWT.SecretKey)
	if err != nil {
		logs.PErrorf("解析JWT失败: %v, accessToken: %s, secretKey: %s", err, req.Token, s.config.JWT.SecretKey)
		return 0, CError.InvalidToken
	}

	// 确认是访问令牌而非刷新令牌
	if claims.TokenType != "access" {
		logs.PErrorf("令牌类型错误，期望access token，得到: %s", claims.TokenType)
		return 0, CError.InvalidToken
	}

	//判断token的黑名单的逻辑
	if err := s.userLogin.FindUserLoginToken(ctx, &dto.UserTokenData{
		UserID:    claims.UserCode,
		ProductID: claims.ProductId,
		ClientID:  req.ClientId,
	}); err != nil {
		logs.Warnf("failed to get FindUserLoginToken error: %s", err)
		return 0, CError.ErrorUserTokenBlackList
	}

	//  如果用户被禁掉了那么用户生成token的时候 直接让用户掉线
	pauseKey := PauseAccessPrefix + claims.ProductId + "_" + claims.UserCode
	exists, err := client.Redis.Exists(ctx, pauseKey).Result()
	if err == nil && exists == 1 {
		// 那么直接让用户掉线 && 删除各个token
		client.Redis.Del(ctx, AccessTokenPrefix+claims.ID)
		client.Redis.Del(ctx, UserPrefix+claims.UserCode)
		return 0, CError.ErrorUserBlocked
	}

	// 检查令牌是否被撤销
	tokenKey := AccessTokenPrefix + claims.ID
	_, err = client.Redis.Get(ctx, tokenKey).Result()
	if err != nil {
		logs.PErrorf("令牌已被撤销: %v", err)
		return 0, CError.InvalidToken
	}

	ctx.Set(consts.OrgCode, claims.OrgCode)
	ctx.Set(consts.UserCode, claims.UserCode)
	ctx.Set(consts.UserName, claims.Username)
	ctx.Set(consts.ProductID, claims.ProductId)

	expireTime := claims.ExpiresAt.Time.Unix()
	nowTime := time.Now().Unix()
	return expireTime - nowTime, nil
}

// 获取用户信息
func (s *authService) getAuthUserInfo(ctx context.Context, claims *commondto.JWTClaims) (*dto.AuthUserInfo, error) {
	// 从Redis中获取用户信息
	userCodeTokenKey := UserPrefix + claims.UserCode
	userJSON, err := client.Redis.Get(ctx, userCodeTokenKey).Result()
	if err == nil {
		return dto.UnmarshalUserInfo(userJSON)
	}

	// 从数据库中查询用户信息
	var user *dto.AuthUserInfo
	err = s.organizationUserCollection.FindOne(ctx, bson.M{"product_id": claims.ProductId, "user_code": claims.UserCode, "status": bson.M{"$ne": dto.UserStatusDelete}}).Decode(&user)
	if err != nil {
		logs.PErrorf("从数据库中查询用户信息失败: %v", err)
		return nil, errors.New("user not found")
	}

	// 获取组织信息
	org := &dto.Organization{}
	if err := s.organizationCollection.FindOne(ctx, bson.M{"org_code": user.OrgCode, "product_id": claims.ProductId}).Decode(&org); err != nil {
		logs.Errorf("根据产品id和orgcode查询组织信息失败: %v", err)
	}

	user.OrgName = org.OrgName

	// 将用户信息存储到Redis中
	userBytes, err := json.Marshal(user)
	if err == nil {
		accessExpiry := 24 * time.Hour // 默认24小时
		confAccessExpiry := s.config.JWT.AccessExpiry
		if confAccessExpiry != 0 {
			// 使用配置中的过期时间
			accessExpiry = time.Duration(confAccessExpiry) * time.Second
		}

		if err := client.Redis.Set(ctx, userCodeTokenKey, string(userBytes), accessExpiry).Err(); err != nil {
			logs.Errorf("存储用户ID 用户信息失败: %v", err)
		}
	}
	return user, nil
}

// ThirdPartyLogin 实现第三方登录
func (s *authService) ThirdPartyLogin(ctx context.Context, req *dto.ThirdPartyRequest) (*dto.TokenResponse, error) {
	// 根据不同的提供商处理第三方登录
	var user *dto.AuthUserInfo
	var err error

	switch req.Provider {
	case consts.WeChat:
		// 微信扫码登录处理
		user, err = s.handleWechatLogin(ctx, req.Code)
	case consts.Dingtalk:
		// 钉钉扫码登录处理
		user, err = s.handleDingtalkLogin(ctx, req.OrgCode, req.Code)
	default:
		logs.PErrorf("不支持的登录提供商: %s", req.Provider)
		return nil, fmt.Errorf("unsupported login provider: %s", req.Provider)
	}

	if err != nil {
		return nil, err
	}

	go s.syncUserLoginTicket(ctx, user.UserCode, req.Ticket, req.Ticket)

	user.LoginMethod = req.Provider
	user.ClientID = req.ClientID

	return s.generateTokens(ctx, user)

}

// Logout 实现登出
func (s *authService) Logout(ctx context.Context, accessToken string) error {
	// 从Redis中删除令牌（撤销）
	// 解析JWT
	claims, err := utils.ParseJWT(accessToken, s.config.JWT.SecretKey)
	if err != nil {
		logs.PErrorf("解析JWT失败: %v, accessToken: %s, secretKey: %s", err, accessToken, s.config.JWT.SecretKey)
		return errors.New("logout failed")
	}

	tokenKey := AccessTokenPrefix + claims.ID
	_, err = client.Redis.Del(ctx, tokenKey).Result()
	if err != nil {
		logs.PErrorf("删除令牌失败: %v, tokenKey: %s", err, tokenKey)
	}

	refreshTokenKey := RefreshTokenPrefix + claims.ID
	_, err = client.Redis.Del(ctx, refreshTokenKey).Result()
	if err != nil {
		logs.PErrorf("删除刷新令牌失败: %v, refreshTokenKey: %s", err, refreshTokenKey)
	}

	// 可选：尝试解析JWT获取刷新令牌相关信息，也可以撤销关联的刷新令牌
	// 但客户端应当负责丢弃刷新令牌

	return nil
}

// 生成访问令牌和刷新令牌
func (s *authService) generateTokens(ctx context.Context, user *dto.AuthUserInfo) (*dto.TokenResponse, error) {
	accessExpiry := 24 * time.Hour // 默认24小时

	confAccessExpiry := s.config.JWT.AccessExpiry
	if confAccessExpiry != 0 {
		// 使用配置中的过期时间
		accessExpiry = time.Duration(confAccessExpiry) * time.Second
	}

	refreshExpiry := 30 * 24 * time.Hour // 默认30天
	confRefreshExpiry := s.config.JWT.RefreshExpiry
	if confRefreshExpiry != 0 {
		// 使用配置中的过期时间
		refreshExpiry = time.Duration(confRefreshExpiry) * time.Second
	}
	partnerID := user.ProductID + "_" + user.UserCode + ":" + uuid.New().String()
	// Generate access token
	accessClaims := utils.GenerateAccessTokenClaims(user, partnerID, accessExpiry, s.config.JWT.Issuer)
	logs.PInfof("Generating access token with expiry: %v", accessExpiry)
	accessToken, err := utils.GenerateJWT(&accessClaims, s.config.JWT.SecretKey)
	if err != nil {
		return nil, fmt.Errorf("failed to generate access token: %s", err)
	}

	// Generate refresh token
	refreshClaims := utils.GenerateRefreshTokenClaims(user, partnerID, refreshExpiry, s.config.JWT.Issuer)
	refreshToken, err := utils.GenerateJWT(&refreshClaims, s.config.JWT.SecretKey)
	if err != nil {
		return nil, fmt.Errorf("failed to generate refresh token: %s", err)
	}

	// Store tokens in Redis
	accessTokenKey := AccessTokenPrefix + accessClaims.ID
	refreshTokenKey := RefreshTokenPrefix + refreshClaims.ID
	userCodeTokenKey := UserPrefix + user.UserCode

	//  如果用户被禁掉了那么用户生成token的时候 直接让用户掉线
	pauseKey := PauseAccessPrefix + user.ProductID + "_" + user.UserCode
	exists, err := client.Redis.Exists(ctx, pauseKey).Result()
	if err == nil && exists == 1 {
		// 那么直接让用户掉线 && 删除各个token
		client.Redis.Del(ctx, accessTokenKey)
		client.Redis.Del(ctx, refreshTokenKey)
		client.Redis.Del(ctx, userCodeTokenKey)
		return nil, CError.ErrorUserBlocked
	}
	// 将用户ID存储在Redis中，与令牌关联
	if err := client.Redis.Set(ctx, accessTokenKey, "valid", accessExpiry).Err(); err != nil {
		logs.PErrorf("存储访问令牌失败: %v", err)
		return nil, errors.New("internal server error")
	}

	if err := client.Redis.Set(ctx, refreshTokenKey, "valid", refreshExpiry).Err(); err != nil {
		logs.PErrorf("存储刷新令牌失败: %v", err)
		return nil, errors.New("internal server error")
	}

	userBytes, _ := json.Marshal(user)
	if err := client.Redis.Set(ctx, userCodeTokenKey, string(userBytes), accessExpiry).Err(); err != nil {
		logs.Errorf("存储用户ID 用户信息失败: %v", err)
	}

	go func() {
		updateValue := bson.M{"is_active": true}
		if user.PasswordReset == dto.UserPasswordNotReset {
			updateValue["password_reset"] = dto.UserPasswordNotReset
		}

		if _, err := s.organizationUserCollection.UpdateOne(context.Background(),
			bson.M{
				"user_code": user.UserCode, "status": bson.M{"$ne": dto.UserStatusDelete}, "product_id": user.ProductID, "is_active": bson.M{"$ne": true},
				"org_code": user.OrgCode,
			}, bson.M{"$set": updateValue}); err != nil {
			logs.PErrorf("更新用户的状态度失败，error: %s userCode: %s", err, user.UserCode)
		}
	}()

	var domain string
	portalConfig, err := s.portal.FindPortalConfig(ctx, user.ProductID)
	if err != nil {
		logs.PErrorf("portal GetPortalConfig info error: %s", err)
	} else {
		domain = portalConfig.Configs.Domain
	}

	// 异步处理
	go func() {
		_, recordErr := s.userLogin.RecordUserLoginData(context.Background(), &dto.UserLoginRecordStruct{
			Phone:       user.Phone,
			Email:       user.Email,
			ProductID:   user.ProductID,
			ClientID:    user.ClientID,
			Token:       accessToken,
			ExpiatedAt:  int(accessExpiry.Seconds()),
			LoginMethod: user.LoginMethod,
		})
		if recordErr != nil {
			logs.Errorf("RecordUserLoginData failed: %v", recordErr)
		}
	}()

	var checkAgreement bool
	if agr, err := agreement.NewUserAgreement().GetUserAgreement(ctx, user.ProductID, "", user.UserCode, user.ClientID); err == nil && agr != nil {
		checkAgreement = true
	}

	return &dto.TokenResponse{
		AccessToken:    accessToken,
		RefreshToken:   refreshToken,
		TokenType:      "Bearer",
		ExpiresIn:      int(accessExpiry.Seconds()),
		UserCode:       user.UserCode,
		Username:       user.Username,
		ProductID:      user.ProductID,
		OrgCode:        user.OrgCode,
		OrgName:        user.OrgName,
		PasswordReset:  user.PasswordReset,
		Domain:         domain,
		CheckAgreement: checkAgreement,
		Phone:          user.Phone,
		Email:          user.Email,
	}, nil
}

// 处理微信扫码登录
func (s *authService) handleWechatLogin(ctx context.Context, code string) (*dto.AuthUserInfo, error) {
	// TODO: 实现微信扫码登录，需要调用微信API获取用户信息
	// 这里是示例代码，实际实现需要集成微信OAuth2.0
	logs.PInfof("处理微信扫码登录，code: %s", code)
	return nil, errors.New("not implemented")
}

// 处理钉钉扫码登录
func (s *authService) handleDingtalkLogin(ctx context.Context, orgCode string, code string) (*dto.AuthUserInfo, error) {
	// TODO: 实现钉钉扫码登录，需要调用钉钉API获取用户信息
	// 这里是示例代码，实际实现需要集成钉钉OAuth2.0
	logs.PInfof("处理钉钉扫码登录，orgCode: %s, code: %s", orgCode, code)

	// 获取用户token
	accessToken, organization, err := s.getDingtalkUserToken(code, orgCode)
	if err != nil {
		logs.PErrorf("获取用户token失败: %v", err)
		return nil, err
	}

	// 获取用户信息
	userInfo, err := s.getDingUserInfo(accessToken, "me")
	if err != nil {
		logs.PErrorf("获取用户信息失败: %v", err)
		return nil, err
	}
	fmt.Printf("userInfo data is %+v\n", userInfo)

	// 根据unionId和appKey 判断是否存在扫码用户的信息
	organizationUser, err := s.getOrganizationUser(organization.OrgCode, userInfo["unionId"].(string))
	if err != nil {
		logs.PErrorf("获取组织用户信息失败: %v", err)
		return nil, err
	}
	if organizationUser.UserCode == "" {
		logs.PErrorf("获取组织用户信息失败: %v", err)
		return nil, err
	}

	if organizationUser.Status != 0 {
		logs.PErrorf("用户已被禁用: %s", organizationUser.UserName)
		return nil, CError.ErrorUserBlocked
	}

	return &dto.AuthUserInfo{
		ProductID:     organizationUser.ProductID,
		OrgCode:       organizationUser.OrgCode,
		UserCode:      organizationUser.UserCode,
		OrgName:       organization.OrgName,
		Username:      organizationUser.UserName,
		Email:         organizationUser.Email,
		Phone:         organizationUser.Mobile,
		PasswordReset: 2,
	}, nil
}

// 根据authCode，调用服务端获取用户token接口，获取用户个人token
func (s *authService) getDingtalkUserToken(authCode string, orgCode string) (string, dto.Organization, error) {
	// 获取appKey对应的secret
	ctx := context.Background()
	var organization dto.Organization
	err := s.organizationCollection.FindOne(ctx, bson.M{"org_code": orgCode, "status": 1}).Decode(&organization)
	if err != nil {
		logs.PErrorf("获取organization失败: %v, orgCode: %s", err, orgCode)
		return "", organization, err
	}
	if organization.AppKey == "" || organization.Secret == "" {
		logs.PErrorf("organization 中appKey或secret为空, orgCode: %s, appKey: %s, secret: %s", orgCode, organization.AppKey, organization.Secret)
		return "", organization, errors.New("orgCode对应的secret为空")
	}

	logs.PInfof("获取orgCode对应的secret成功: %v", organization)
	// 构建请求URL
	authRURL := "https://api.dingtalk.com/v1.0/oauth2/userAccessToken"

	mapData := map[string]string{
		"clientId":     organization.AppKey,
		"clientSecret": organization.Secret,
		"code":         authCode,
		"grantType":    "authorization_code",
	}
	jsonData, err := json.Marshal(mapData)
	if err != nil {
		return "", organization, err
	}

	// 发送GET请求
	response, err := http.Post(authRURL, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		logs.PErrorf("钉钉发送GET请求失败: %v", err)
		return "", organization, err
	}
	defer func() {
		_ = response.Body.Close()
	}()
	// 读取响应
	body, err := io.ReadAll(response.Body)
	if err != nil {
		logs.PErrorf("钉钉读取响应失败: %v", err)
		return "", organization, err
	}

	// 解析响应
	var result map[string]any
	err = json.Unmarshal(body, &result)
	if err != nil {
		logs.PErrorf("钉钉解析响应失败: %v", err)
		return "", organization, err
	}
	logs.PInfof("钉钉获取userAccessToken响应: %v", result)
	if result["accessToken"] == nil {
		logs.PErrorf("钉钉获取userAccessToken失败: %v", result)
		return "", organization, errors.New("获取userAccessToken失败")
	}

	return result["accessToken"].(string), organization, nil
}

// 根据获取的accessToken，调用服务端获取用户信息接口，获取用户信息；获取当前授权人的信息，unionId参数值请传字符串me
func (s *authService) getDingUserInfo(accessToken string, unionID string) (map[string]any, error) {
	// 构建请求URL
	authRURL := fmt.Sprintf("https://api.dingtalk.com/v1.0/contact/users/%s", unionID)
	// 创建请求
	req, err := http.NewRequest("GET", authRURL, nil)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("x-acs-dingtalk-access-token", accessToken)
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	authClient := &http.Client{}
	resp, err := authClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}
	defer func() { _ = resp.Body.Close() }()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	// 解析响应
	var result map[string]any
	err = json.Unmarshal(body, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

// 根据unionId和appKey 判断是否存在扫码用户的信息
func (s *authService) getOrganizationUser(orgCode string, unionID string) (dto.OrganizationUser, error) {
	ctx := context.Background()
	var organizationUser dto.OrganizationUser
	err := s.organizationUserCollection.FindOne(ctx, bson.M{"org_code": orgCode, "source_code": unionID, "status": bson.M{"$ne": dto.UserStatusDelete}}).Decode(&organizationUser)
	if err != nil {
		logs.PErrorf("data error :%s", err)
		return dto.OrganizationUser{}, err
	}
	return organizationUser, nil
}

// GetOrgList 根据productId获取组织列表
func (s *authService) GetOrgList(ctx context.Context, productID string) ([]dto.OrgListResponse, error) {
	// 根据productId获取组织列表
	cursor, err := s.organizationCollection.Find(ctx, bson.M{"product_id": productID, "status": 1, "range_type": bson.M{"$ne": dto.RangeTypeGuest}})
	if err != nil {
		return nil, err
	}
	defer func() {
		_ = cursor.Close(ctx)
	}()

	var orgs []dto.OrgListResponse
	err = cursor.All(ctx, &orgs)
	if err != nil {
		return nil, err
	}
	return orgs, nil
}

// GetAppList 根据当前用户获取应用列表
func (s *authService) GetAppList(ctx context.Context, userCode string, page int, limit int) ([]dto.AppItem, int64, error) {
	// 根据当前用户获取应用列表
	total, err := s.appCollection.CountDocuments(ctx, bson.M{"is_deleted": 0})
	if err != nil {
		return nil, 0, err
	}

	skip := (page - 1) * limit
	opts := options.Find().SetSkip(int64(skip)).SetLimit(int64(limit)).SetSort(bson.M{"created_at": -1})
	cursor, err := s.appCollection.Find(ctx, bson.M{"is_deleted": 0}, opts)
	if err != nil {
		return nil, 0, err
	}
	defer func() {
		_ = cursor.Close(ctx)
	}()

	var apps []dto.AppItem
	err = cursor.All(ctx, &apps)
	if err != nil {
		return nil, 0, err
	}
	return apps, total, nil
}

func (s *authService) getUserInfoByPhoneOrPassword(ctx context.Context, data *dto.GetUserInfoByPhoneOrEmail) (*dto.UserInfo, error) {
	filter := bson.M{"product_id": data.ProductID, "status": bson.M{"$ne": dto.UserStatusDelete}}

	if data.OrgCode != "" {
		filter["org_code"] = data.OrgCode
	}

	if data.Phone != "" {
		filter["mobile"] = data.Phone
	} else if data.Email != "" {
		filter["email"] = data.Email
	} else if data.Pwd != "" {
		filter["password"] = data.Pwd
	} else {
		return nil, errors.New("手机号或邮箱或密码不能为空")
	}

	user := &dto.UserInfo{}
	if err := s.organizationUserCollection.FindOne(ctx, filter).Decode(&user); err != nil {
		logs.Errorf("failed to FindOne Member error: %s", err)
		return nil, err
	}
	return user, nil
}

func (s *authService) ResetPassword(ctx context.Context, req *dto.ResetPwdReq) error {
	user, err := s.getUserInfoByPhoneOrPassword(ctx, &dto.GetUserInfoByPhoneOrEmail{
		OrgCode:   req.OrgCode,
		Phone:     req.Phone,
		ProductID: req.ProductID,
	})
	if err != nil {
		logs.Errorf("can't find this orguser error: %s", err)
		return CError.ErrorUserNotFound
	}

	if utils.VerifyPassword(req.Password, user.PasswordHash) {
		return CError.ErrorResetPassword
	}
	if user.Status == 1 {
		logs.Errorf("用户%s被暂停使用", user.Username)
		return CError.ErrorUserBlocked
	}

	hashPwd, _ := utils.HashPassword(req.Password)

	updateRes, err := s.organizationUserCollection.UpdateOne(ctx,
		bson.M{"org_code": req.OrgCode, "product_id": req.ProductID, "mobile": req.Phone, "status": bson.M{"$ne": dto.UserStatusDelete}},
		bson.M{"$set": bson.M{"password": hashPwd}})
	if err != nil || updateRes.ModifiedCount == 0 {
		logs.Errorf("failed to reset password error: %s", err)
		return CError.ResetPwdFailed
	}
	return nil
}

func (s *authService) LdapLogin(ctx context.Context, req *dto.LdapLoginRequest) (*dto.TokenResponse, error) {
	org := &dto.Organization{}
	if err := s.organizationCollection.FindOne(ctx, bson.M{
		"product_id":  req.ProductID,
		"source_type": "LDAP", "org_code": req.OrgCode,
	}).Decode(&org); err != nil {
		logs.Errorf("根据产品id和orgcode查询组织信息失败: %v", err)
		return nil, CError.ErrorPassword
	}

	employee, err := baseRequest(ctx, req.Account, req.Password, org)
	if err != nil || !employee.Success {
		logs.Errorf("根据baseRequest查询组织信息失败: %v", err)
		return nil, CError.ErrorPassword
	}

	user := &dto.UserInfo{}
	if err := s.organizationUserCollection.FindOne(ctx, bson.M{
		"product_id": req.ProductID,
		"org_code":   req.OrgCode, "account": employee.Employee.Account, "source_code": employee.Employee.SourceCode, "status": bson.M{"$ne": dto.UserStatusDelete},
	}).Decode(&user); err != nil {
		logs.Errorf("根据用户account %s 和code %s 查询组织信息失败: %v", req.Account, req.Password, err)
		return nil, CError.ErrorPassword
	}

	return s.generateTokens(ctx, &dto.AuthUserInfo{
		ProductID:     user.ProductID,
		OrgCode:       user.OrgCode,
		UserCode:      user.UserCode,
		Username:      user.Username,
		Email:         user.Email,
		Phone:         user.Phone,
		OrgName:       org.OrgName,
		ClientID:      req.ClientID,
		LoginMethod:   consts.Ldap,
		PasswordReset: 2,
	})

}

func baseRequest(ctx context.Context, account, pwd string, org *dto.Organization) (*dto.EmployeeResponse, error) {
	requestURL := fmt.Sprintf("%s/ldap/authenticate", config.Config().ThirdParty.Address)

	var target string
	if len(org.LdapConfig.ConnectorCluster) > 0 && org.SourceType == "LDAP" {
		idx := rand.IntN(len(org.LdapConfig.ConnectorCluster))
		target = fmt.Sprintf("%s:%s:%s", org.ProductID, org.OrgCode, org.LdapConfig.ConnectorCluster[idx])
	}
	if org.LdapConfig.IsInternal && org.LdapConfig.MirrorAddr == "" {
		return nil, errors.New("this org ldap Config mirrorAddress is empty")
	}

	ldapURL := org.LdapConfig.LdapURL
	if ldapURL != "" && org.LdapConfig.MirrorAddr != "" {
		u, err := url.Parse(ldapURL)
		if err != nil {
			logs.Errorf("ldap url error: %v", err)
		} else {
			u.Host = org.LdapConfig.MirrorAddr
			ldapURL = u.String()
		}
	}

	jsonByte, _ := json.Marshal(map[string]any{
		"url":              ldapURL,
		"baseDN":           org.LdapConfig.LdapDN,
		"username":         org.LdapConfig.LdapUsername,
		"password":         org.LdapConfig.LdapPassword,
		"employeeAccount":  account,
		"employeePassword": pwd,
		"employeeFieldMap": map[string]any{
			"source_code": "objectGUID",
			"name":        "name",
			"account":     "sAMAccountName",
		},
		"proxyConfig": map[string]string{
			"target": target,
		},
	})

	req, err := http.NewRequestWithContext(ctx, http.MethodPost, requestURL, bytes.NewReader(jsonByte))
	if err != nil {
		logs.Errorf("创建请求失败: %s", err)
		return nil, err
	}
	req.Header.Set("Content-Type", "application/json")
	authClient := http.Client{}
	resp, err := authClient.Do(req)
	if err != nil {
		logs.Errorf("请求失败: %s", err)
		return nil, fmt.Errorf("请求失败: %s", err)
	}
	employee := &dto.EmployeeResponse{}
	bytesData, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应内容失败: %s", err)
	}
	if err := json.Unmarshal(bytesData, &employee); err != nil {
		return nil, fmt.Errorf("解析响应内容失败: %s", err)
	}
	return employee, nil
}

func (s *authService) Authenticate(ctx context.Context, req *dto.AuthenticateRequest) (bool, error) {
	logs.Infof("开始验证,req %+v", req)
	authSetting, err := s.setting.FindEnterpriseSetting(ctx, req.ProductID, "join_company")
	if err != nil {
		return false, err
	}

	if authSetting == nil {
		return false, nil
	}

	if req.AuthenticateType == dto.StatusCheck {
		return authSetting.GetJoinCompanySetting().OpenEnterprisePwd, nil
	}
	if utils.Md5Str(authSetting.GetJoinCompanySetting().Pwd) != req.Pwd {
		logs.Infof("datda value is %s ,req.pwd : %s", utils.Md5Str(authSetting.GetJoinCompanySetting().Pwd), req.Pwd)
		return false, CError.New(10000, "密码错误", "Password is wrong")
	}

	return utils.Md5Str(authSetting.GetJoinCompanySetting().Pwd) == req.Pwd && authSetting.GetJoinCompanySetting().OpenEnterprisePwd, nil
}

func (s *authService) GetOrganizationConfig(ctx context.Context, productID, orgCOde string) (*dto.Organization, error) {
	org := &dto.Organization{}
	if err := s.organizationCollection.FindOne(ctx, bson.M{"product_id": productID, "org_code": orgCOde, "status": 1}).Decode(&org); err != nil {
		return nil, err
	}

	return org, nil
}

func (s *authService) LoginResetPwd(ctx context.Context, userCode string, req *dto.ResetPwdReq) error {
	user := &dto.OrganizationUser{}
	if err := s.organizationUserCollection.FindOne(ctx, bson.M{
		"org_code":   req.OrgCode,
		"product_id": req.ProductID,
		"user_code":  userCode,
		"status":     bson.M{"$ne": dto.UserStatusDelete},
	}).Decode(&user); err != nil {
		logs.PErrorf("failed to call Login ResetPwd user find ,error: %s", err)
		return CError.ResetPwdFailed
	}

	if utils.VerifyPassword(req.Password, user.Password) {
		return CError.ErrorResetPassword
	}

	hashPwd, _ := utils.HashPassword(req.Password)
	updateRes, err := s.organizationUserCollection.UpdateOne(ctx,
		bson.M{"org_code": req.OrgCode, "product_id": req.ProductID, "status": bson.M{"$ne": dto.UserStatusDelete}, "user_code": userCode},
		bson.M{"$set": bson.M{"password": hashPwd, "password_reset": dto.UserPasswordNotReset}})
	if err != nil || updateRes.ModifiedCount == 0 {
		logs.Errorf("failed to reset password error: %s", err)
		return CError.ResetPwdFailed
	}
	return nil
}

func (s *authService) ValidatePassword(ctx context.Context, req *dto.ResetPwdReq) error {
	org, err := s.GetOrganizationConfig(ctx, req.ProductID, req.OrgCode)
	if err != nil || org == nil {
		// 根据业务去判定
		logs.PErrorf("failed to call orgConfig error: %s", err)
		return nil
	}

	pwdConfig := org.SourceConfig.PwdConfig

	// 校验密码 强度
	if !pwdConfig.AllowSelfChange {
		// common.ResponseResult(ctx, errors.ErrorNeedResetPwd)
		return CError.ErrorSelfChangePwd
	}

	var hasUpper, hasLower, hasNumber, hasSpecial bool
	for _, c := range req.Password {
		switch {
		case unicode.IsUpper(c):
			hasUpper = true
		case unicode.IsLower(c):
			hasLower = true
		case unicode.IsDigit(c):
			hasNumber = true
		case unicode.IsPunct(c) || unicode.IsSymbol(c):
			hasSpecial = true
		}
	}

	if pwdConfig.PwdReqSpecial && !hasSpecial {
		return CError.ErrorPasswordSpecial
	}
	if pwdConfig.PwdReqDigits && !hasNumber {
		return CError.ErrorPasswordNumber
	}
	if pwdConfig.PwdReqLower && !hasLower {
		return CError.ErrorPasswordLower
	}
	if pwdConfig.PwdReqUpper && !hasUpper {
		return CError.ErrorPasswordUpper
	}
	if len(req.Password) < pwdConfig.PwdMinLen {
		return CError.ErrorPasswordLength
	}
	return nil
}

func (s *authService) SetUserInfoStatusLocked(ctx context.Context, req *dto.LoginAccountIP) error {
	filter := bson.M{
		"product_id": req.ProductID,
		"status":     bson.M{"$ne": dto.UserStatusDelete},
		"org_code":   req.OrgCode,
	}

	if req.Account != "" {
		filter["$or"] = []bson.M{
			{"mobile": req.Account},
			{"account": req.Account},
			{"user_name": req.Account},
		}
	}

	if _, err := s.organizationUserCollection.UpdateOne(ctx, filter, bson.M{"$set": bson.M{
		"status": dto.UserStatusLocked,
	}}); err != nil {
		logs.PErrorf("failed to Update user status,error: %s, filter: %+v", err, filter)
	}
	return nil
}

func (s *authService) LdapDomainAccountLogin(ctx context.Context, req *dto.LdapDomainAccountLoginRequest) (*dto.TokenResponse, error) {
	var (
		org          dto.Organization
		organization []dto.Organization
	)

	// 域名校验
	if req.DomainName == "" {
		logs.Errorf("LdapDomainAccountLogin 域信息不存在, DomainName: %s", req.DomainName)
		return nil, CError.ErrorOrgDomainNotExist
	}

	// 查询组织信息
	orgFilter := bson.M{
		"product_id":  req.ProductID,
		"source_type": "LDAP",
		"status":      1,
	}

	if req.OrgCode != "" {
		orgFilter["org_code"] = req.OrgCode
	}

	cursor, err := s.organizationCollection.Find(ctx, orgFilter)

	if err != nil {
		logs.Errorf("fail get Organization Find error: %v", err)
		return nil, CError.ErrorOrgDomainNotExist
	}

	defer func(cursor *mongo.Cursor, ctx context.Context) {
		_ = cursor.Close(ctx)
	}(cursor, ctx)

	if err = cursor.All(ctx, &organization); err != nil {
		logs.Errorf("fail get Organization cursor.All error: %v", err)
		return nil, CError.ErrorOrgDomainNotExist
	}

	var found bool
	for _, item := range organization {
		if item.LdapConfig != nil && isDomainInLDAPDN(req.DomainName, item.LdapConfig.LdapDN) {
			org = item
			found = true
			break
		}
	}

	// 未找到匹配的域信息
	if !found {
		logs.Errorf("LdapDomainAccountLogin 组织内未找到域信息, DomainName: %s 组织信息 %v", req.DomainName, org)
		return nil, CError.ErrorOrgDomainNotExist
	}

	// 查询用户信息
	userFilter := bson.M{
		"product_id":  req.ProductID,
		"org_code":    org.OrgCode,
		"account":     req.Account,
		"source_type": "LDAP",
		"status":      dto.UserStatusNormal,
	}

	user := &dto.UserInfo{}
	if err := s.organizationUserCollection.FindOne(ctx, userFilter).Decode(user); err != nil {
		logs.Errorf("根据用户account %s 和code %s 获取用户信息失败: %v , param: %v", req.Account, req.OrgCode, err, req)
		return nil, CError.ErrorUserNotFound
	}

	// 生成认证用户信息并生成令牌
	authUserInfo := &dto.AuthUserInfo{
		ProductID:   user.ProductID,
		OrgCode:     user.OrgCode,
		UserCode:    user.UserCode,
		Username:    user.Username,
		Email:       user.Email,
		Phone:       user.Phone,
		OrgName:     org.OrgName,
		ClientID:    req.ClientID,
		LoginMethod: consts.Ldap,
	}

	return s.generateTokens(ctx, authUserInfo)
}

func (s *authService) GuestLogin(ctx context.Context, req *dto.GuestLoginRequest) (*dto.TokenResponse, error) {
	orgUser, err := s.findOrCreateUser(ctx, req)
	if err != nil {
		logs.Errorf("failed to call GuestLogin find or create user error: %s", err)
		return nil, CError.ErrorGuestLogin
	}

	return s.generateTokens(ctx, orgUser)
}

func (s *authService) findOrCreateOrg(ctx context.Context, req *dto.GuestLoginRequest) (*dto.Organization, *dto.Department, error) {
	// 先查询DataValue
	org := &dto.Organization{}

	filter := bson.D{
		{Key: "product_id", Value: req.ProductID},
		{Key: "source_type", Value: dto.GUEST},
		{Key: "range_type", Value: dto.RangeTypeGuest},
		{Key: "status", Value: 1},
	}

	update := bson.D{
		{Key: "$set", Value: bson.D{
			{Key: "updated_at", Value: time.Now().Unix()},
		}},
		{
			Key: "$setOnInsert", Value: bson.D{
				{Key: "org_code", Value: utils.GenerateOrgCode()},
				{Key: "org_name", Value: dto.GUEST},
				{Key: "created_at", Value: time.Now().Unix()},
				{Key: "sync_status", Value: 0},
				{Key: "auto_sync", Value: 0},
				{Key: "auto_sync_time", Value: 0},
			},
		},
	}
	if err := s.organizationCollection.FindOneAndUpdate(ctx, filter, update,
		options.FindOneAndUpdate().SetUpsert(true).SetReturnDocument(options.After)).Decode(&org); err != nil {

		logs.PErrorf("获取 organization 信息数据 %s", err)
		return nil, nil, err
	}

	departFilter := bson.D{
		{Key: "product_id", Value: req.ProductID},
		{Key: "org_code", Value: org.OrgCode},
		{Key: "status", Value: 1},
	}
	departUpdate := bson.D{
		{Key: "$set", Value: bson.D{
			{Key: "updated_at", Value: time.Now().Unix()},
		}},
		{
			Key: "$setOnInsert", Value: bson.D{
				{Key: "depart_code", Value: org.OrgCode},
				{Key: "depart_name", Value: fmt.Sprintf("%s_org_%s_depart", req.ProductID, dto.GUEST)},
				{Key: "parent_dept_code", Value: "0"},
				{Key: "has_sub_dept", Value: false},
				{Key: "created_at", Value: time.Now().Unix()},
				{Key: "import_batch_no", Value: uuid.New().String()},
			},
		},
	}

	department := &dto.Department{}
	if err := s.departmentCollection.FindOneAndUpdate(ctx, departFilter, departUpdate,
		options.FindOneAndUpdate().SetReturnDocument(options.After).SetUpsert(true)).Decode(&department); err != nil {
		logs.PErrorf("获取 department 信息数据 %s", err)
		return nil, nil, err
	}

	return org, department, nil
}

func (s *authService) findOrCreateUser(ctx context.Context, req *dto.GuestLoginRequest) (*dto.AuthUserInfo, error) {
	targetOrg, targetDepart, err := s.findOrCreateOrg(ctx, req)
	if err != nil {
		logs.Errorf("failed to call GuestLogin find or create error: %s", err)
		return nil, err
	}

	// 构造虚拟用户的数据
	filter := bson.D{
		{Key: "product_id", Value: req.ProductID},
		{Key: "org_code", Value: targetOrg.OrgCode},
		{Key: "source_type", Value: dto.GUEST},
		{Key: "user_credentials", Value: req.Ticket},
	}

	userName := fmt.Sprintf("GUEST_%s", utils.GetHashPrefix(uuid.NewString(), 4))

	update := bson.D{
		{
			Key: "$set", Value: bson.D{
				{Key: "updated_at", Value: time.Now().Unix()},
			},
		},
		{
			Key: "$setOnInsert", Value: bson.D{
				{Key: "user_code", Value: utils.GenerateUserCode()},
				{Key: "user_name", Value: userName},
				{Key: "account", Value: userName},
				{Key: "email", Value: ""},
				{Key: "password_reset", Value: dto.UserPasswordNotReset},
				{Key: "status", Value: 0},
				{Key: "dept_path", Value: [][]string{{targetOrg.OrgCode}}},
				{Key: "created_at", Value: time.Now().Unix()},
			},
		},
	}

	orgUser := &dto.OrganizationUser{}

	if err := s.organizationUserCollection.FindOneAndUpdate(ctx, filter, update,
		options.FindOneAndUpdate().SetUpsert(true).SetReturnDocument(options.After)).Decode(&orgUser); err != nil {
		logs.PErrorf("获取 organization_user 信息数据 %s", err)
		return nil, err
	}
	// 绑定用户关系数据

	relationFilter := bson.D{
		{Key: "product_id", Value: req.ProductID},
		{Key: "dept_code", Value: targetDepart.DeptCode},
		{Key: "user_code", Value: orgUser.UserCode},
	}

	relationUpdate := bson.D{
		{Key: "$set", Value: bson.D{
			{Key: "org_code", Value: targetOrg.OrgCode},
		}},
		{
			Key: "$setOnInsert", Value: bson.D{
				//{Key: "org_code", Value: targetOrg.OrgCode},
				{Key: "user_code", Value: orgUser.UserCode},
				{Key: "dept_path", Value: [][]string{{targetOrg.OrgCode}}},
			},
		},
	}

	if err := s.departmentUserRelationCollection.FindOneAndUpdate(ctx, relationFilter, relationUpdate,
		options.FindOneAndUpdate().SetReturnDocument(options.After).SetUpsert(true)).Decode(&orgUser); err != nil {
		logs.PErrorf("获取 department_user_relation 信息数据 %s", err)
		return nil, err
	}

	return &dto.AuthUserInfo{
		ProductID:     req.ProductID,
		OrgCode:       targetOrg.OrgCode,
		UserCode:      orgUser.UserCode,
		Username:      orgUser.UserName,
		Email:         orgUser.Email,
		Phone:         orgUser.Mobile,
		OrgName:       targetOrg.OrgName,
		PasswordReset: 2,
	}, nil
}

func (s *authService) syncUserLoginTicket(ctx context.Context, userCode, orgCode string, ticket string) {
	if ticket != "" {
		userCredentials, err := utils.Base64Decode(ticket)
		if err != nil {
			logs.Errorf("decode ticket error: %s", err)
			return
		}
		_, err = s.organizationUserCollection.UpdateOne(ctx,
			bson.M{"org_code": orgCode, "user_code": userCode}, bson.M{"$set": bson.M{"user_credentials": userCredentials}})
		if err != nil {
			logs.Errorf("update user_credentials error: %s", err)
		}
	}
}

func isDomainInLDAPDN(domain, ldapDN string) bool {
	domain = strings.ToLower(strings.TrimSpace(domain))
	ldapDN = strings.ToLower(strings.TrimSpace(ldapDN))

	components := strings.Split(ldapDN, ",")
	dcParts := make([]string, len(components))
	for i, comp := range components {
		dcParts[i] = strings.Replace(strings.TrimSpace(comp), "dc=", "", 1)
	}

	joined := strings.Join(dcParts, ".")

	return joined == domain
}

func (s *authService) GetLoginTypeList(ctx context.Context, req *dto.LoginTypeListRequest) (*dto.LoginTypeListResponse, error) {

	var org = &dto.Organization{}
	if err := s.organizationCollection.FindOne(ctx, bson.M{"org_code": req.OrgCode, "product_id": req.ProductID}).Decode(&org); err != nil {
		logs.Warnf("call FindOne error %s", err)

		return nil, err
	}

	var result = make([]dto.LoginTypeListInfo, 0)

	if org.SourceType == string(dto.CUSTOM) {
		result = append(result, dto.LoginTypeListInfo{
			Type:       "Account",
			ObjectCode: "",
		})
	}

	if org.SourceType == string(dto.DINGTALK) {
		result = append(result, dto.LoginTypeListInfo{
			Type:       string(dto.DINGTALK),
			ObjectCode: "",
			Name:       org.OrgName,
		})
	}

	if org.SourceType == string(dto.LDAP) {
		result = append(result, dto.LoginTypeListInfo{
			Type:       "LDAP",
			ObjectCode: "",
			Name:       org.OrgName,
		})
	}

	if org.SSOConfig != nil && org.SSOConfig.SSOCode != "" {
		result = append(result, dto.LoginTypeListInfo{
			Type:       "SSO",
			ObjectCode: org.SSOConfig.SSOCode,
			Name:       org.OrgName,
		})
	}

	return &dto.LoginTypeListResponse{Items: result}, nil

}

func (s *authService) SSOLogin(ctx context.Context, req *dto.SSORequest) (*dto.TokenResponse, error) {

	userInfo, err := s.getUserInfoByPhoneOrPassword(ctx, &dto.GetUserInfoByPhoneOrEmail{
		OrgCode:   req.OrgCode,
		ProductID: req.ProductID,
		Email:     req.Email,
	})
	if err != nil {
		logs.Errorf("getuUserInfoByPhoneOrPassword error: %s", err)
		return nil, CError.ErrorUserNotFound
	}

	return s.generateTokens(ctx, &dto.AuthUserInfo{
		ProductID:     req.ProductID,
		OrgCode:       req.OrgCode,
		UserCode:      userInfo.UserCode,
		Username:      userInfo.Username,
		Email:         userInfo.Email,
		Phone:         userInfo.Phone,
		OrgName:       userInfo.OrgName,
		PasswordReset: userInfo.PasswordReset,
		ClientID:      req.ClientID,
		LoginMethod:   consts.SSO,
	})

}

// VerificationCodeLogin 验证码登录（支持手机号和邮箱）
func (s *authService) VerificationCodeLoginV2(ctx context.Context, req *dto.VerificationCodeLoginRequest) (*dto.TokenResponse, error) {
	// 验证参数：手机号和邮箱不能同时为空，也不能同时有值
	if (req.Phone == "" && req.Email == "") || (req.Email != "" && req.Phone != "") {
		return nil, errors.New("phone and email cannot both be empty or both have values")
	}

	// 构建查询参数
	queryReq := &dto.GetUserInfoByPhoneOrEmail{
		OrgCode:   req.OrgCode,
		ProductID: req.ProductID,
	}

	var loginType, identifier string
	if req.Phone != "" {
		queryReq.Phone = req.Phone
		loginType = consts.PhoneLogin
		identifier = req.Phone
	} else {
		queryReq.Email = req.Email
		loginType = consts.EmailLogin
		identifier = req.Email
	}

	userLogin, err := s.userLogin.FindUserByPhoneOrEmailOrLdap(ctx, &dto.SearchUserInfoRequest{
		ProductID: req.ProductID,
		Phone:     req.Phone,
		Email:     req.Email,
	})
	if err != nil {
		logs.Errorf("FindUserByPhoneOrEmailOrLdap error but not not found: %s", err)
		return nil, CError.ErrorUserLogin
	}
	// 当用户的数据的在里面的话
	if userLogin != nil {
		if userLogin.State == 1 {
			logs.PErrorf("用户已被禁用: %s", userLogin.UserID)
			return nil, CError.ErrorUserBlocked
		}

		// 随机取值
		// 根据手机号或邮箱查询用户信息 因此这部分的逻辑是混乱的
		userInfo, err := s.getUserInfoByPhoneOrPassword(ctx, queryReq)
		if err != nil {
			logs.Errorf("根据%s查询用户信息失败: %v, %s: %s", loginType, err, loginType, identifier)
			return nil, CError.ErrorUserNotFound
		}

		// 查询组织信息,
		org := &dto.Organization{}
		if err := s.organizationCollection.FindOne(ctx, bson.M{"org_code": userInfo.OrgCode, "product_id": req.ProductID, "status": 1}).Decode(&org); err != nil {
			logs.Errorf("根据产品id和orgcode查询组织信息失败: %v", err)
		}

		return s.generateTokensV2(ctx, &dto.AuthUserInfo{
			ProductID:     req.ProductID,
			OrgCode:       org.OrgCode,
			UserCode:      userLogin.UserID,
			Username:      userInfo.Username,
			Email:         req.Email,
			Phone:         req.Phone,
			OrgName:       org.OrgName,
			PasswordReset: userInfo.PasswordReset,
			ClientID:      req.ClientID,
			LoginMethod:   loginType,
		})

	}
	// 去在登陆表的数据中添加一个数据
	userLogin, err = s.userLogin.FindAndInsert(ctx, &dto.UserLoginRecordStruct{
		Phone:       req.Phone,
		Email:       req.Email,
		ProductID:   req.ProductID,
		ClientID:    req.ClientID,
		LoginMethod: loginType,
	})
	if err != nil {
		logs.Errorf("failed to get FindAndInsert error:%s", err)
		return nil, err
	}

	return s.VerificationCodeLoginV2(ctx, req)

}

// 生成访问令牌和刷新令牌
func (s *authService) generateTokensV2(ctx context.Context, user *dto.AuthUserInfo) (*dto.TokenResponse, error) {
	var data = make(map[string]interface{}, 0)
	err := s.userAccountDisposal.FindOne(ctx, bson.M{"product_id": user.ProductID, "user_id": user.UserCode, "disable_account": true}).Decode(&data)
	if err == nil {
		logs.Warnf("get this request had an record %+v", bson.M{"product_id": user.ProductID, "user_id": user.UserCode, "disable_account": true})
		return nil, CError.ErrorUserBan
	}

	accessExpiry := 24 * time.Hour // 默认24小时

	confAccessExpiry := s.config.JWT.AccessExpiry
	if confAccessExpiry != 0 {
		// 使用配置中的过期时间
		accessExpiry = time.Duration(confAccessExpiry) * time.Second
	}

	refreshExpiry := 30 * 24 * time.Hour // 默认30天
	confRefreshExpiry := s.config.JWT.RefreshExpiry
	if confRefreshExpiry != 0 {
		// 使用配置中的过期时间
		refreshExpiry = time.Duration(confRefreshExpiry) * time.Second
	}
	partnerID := user.ProductID + "_" + user.UserCode + ":" + uuid.New().String()
	// Generate access token
	accessClaims := utils.GenerateAccessTokenClaims(user, partnerID, accessExpiry, s.config.JWT.Issuer)
	logs.PInfof("Generating access token with expiry: %v", accessExpiry)
	accessToken, err := utils.GenerateJWT(&accessClaims, s.config.JWT.SecretKey)
	if err != nil {
		return nil, fmt.Errorf("failed to generate access token: %s", err)
	}

	// Generate refresh token
	refreshClaims := utils.GenerateRefreshTokenClaims(user, partnerID, refreshExpiry, s.config.JWT.Issuer)
	refreshToken, err := utils.GenerateJWT(&refreshClaims, s.config.JWT.SecretKey)
	if err != nil {
		return nil, fmt.Errorf("failed to generate refresh token: %s", err)
	}

	// Store tokens in Redis
	accessTokenKey := AccessTokenPrefix + accessClaims.ID
	refreshTokenKey := RefreshTokenPrefix + refreshClaims.ID
	userCodeTokenKey := UserPrefix + user.UserCode

	//  如果用户被禁掉了那么用户生成token的时候 直接让用户掉线
	pauseKey := PauseAccessPrefix + user.ProductID + "_" + user.UserCode
	exists, err := client.Redis.Exists(ctx, pauseKey).Result()
	if err == nil && exists == 1 {
		// 那么直接让用户掉线 && 删除各个token
		client.Redis.Del(ctx, accessTokenKey)
		client.Redis.Del(ctx, refreshTokenKey)
		client.Redis.Del(ctx, userCodeTokenKey)
		return nil, CError.ErrorUserBlocked
	}
	// 将用户ID存储在Redis中，与令牌关联
	if err := client.Redis.Set(ctx, accessTokenKey, "valid", accessExpiry).Err(); err != nil {
		logs.PErrorf("存储访问令牌失败: %v", err)
		return nil, errors.New("internal server error")
	}

	if err := client.Redis.Set(ctx, refreshTokenKey, "valid", refreshExpiry).Err(); err != nil {
		logs.PErrorf("存储刷新令牌失败: %v", err)
		return nil, errors.New("internal server error")
	}

	userBytes, _ := json.Marshal(user)
	if err := client.Redis.Set(ctx, userCodeTokenKey, string(userBytes), accessExpiry).Err(); err != nil {
		logs.Errorf("存储用户ID 用户信息失败: %v", err)
	}

	go func() {
		updateValue := bson.M{"is_active": true}

		baseFilter := bson.M{
			"status": bson.M{"$ne": dto.UserStatusDelete}, "product_id": user.ProductID, "is_active": bson.M{"$ne": true},
		}
		if user.Phone != "" {
			baseFilter["mobile"] = user.Phone
		}
		if user.Email != "" {
			baseFilter["email"] = user.Email
		}
		if user.Account != "" {
			baseFilter["account"] = user.Account
			baseFilter["source_type"] = "LDAP"
		}

		if _, err := s.organizationUserCollection.UpdateMany(context.Background(), baseFilter, bson.M{"$set": updateValue}); err != nil {
			logs.PErrorf("更新用户的状态度失败，error: %s userCode: %s", err, user.UserCode)
		}

		s.userLogin.CheckToken(ctx, &dto.UserTokenData{
			UserID:    user.UserCode,
			ProductID: user.ProductID,
			ClientID:  user.ClientID,
			Token:     accessToken,
			ExpiredAt: time.Now().Add(accessExpiry),
		})

	}()

	var domain string
	portalConfig, err := s.portal.FindPortalConfig(ctx, user.ProductID)
	if err != nil {
		logs.PErrorf("portal GetPortalConfig info error: %s", err)
	} else {
		domain = portalConfig.Configs.Domain
	}

	var checkAgreement bool
	if agr, err := agreement.NewUserAgreement().GetUserAgreement(ctx, user.ProductID, user.UserCode, "", user.ClientID); err == nil && agr != nil {
		checkAgreement = true
	}
	logs.Infof("get userInfo Agreement checkAgreement %v", checkAgreement)

	return &dto.TokenResponse{
		AccessToken:    accessToken,
		RefreshToken:   refreshToken,
		TokenType:      "Bearer",
		ExpiresIn:      int(accessExpiry.Seconds()),
		UserCode:       user.UserCode,
		Username:       user.Username,
		ProductID:      user.ProductID,
		OrgCode:        user.OrgCode,
		OrgName:        user.OrgName,
		PasswordReset:  user.PasswordReset,
		Domain:         domain,
		CheckAgreement: checkAgreement,
		Phone:          user.Phone,
		Email:          user.Email,
	}, nil
}

func (s *authService) LdapDomainAccountLoginV2(ctx context.Context, req *dto.LdapDomainAccountLoginRequest) (*dto.TokenResponse, error) {
	var (
		org          dto.Organization
		organization []dto.Organization
	)

	// 域名校验
	if req.DomainName == "" {
		logs.Errorf("LdapDomainAccountLogin 域信息不存在, DomainName: %s", req.DomainName)
		return nil, CError.ErrorOrgDomainNotExist
	}

	// 查询组织信息
	orgFilter := bson.M{
		"product_id":  req.ProductID,
		"source_type": "LDAP",
		"status":      1,
	}

	if req.OrgCode != "" {
		orgFilter["org_code"] = req.OrgCode
	}

	cursor, err := s.organizationCollection.Find(ctx, orgFilter)

	if err != nil {
		logs.Errorf("fail get Organization Find error: %v", err)
		return nil, CError.ErrorOrgDomainNotExist
	}

	defer func(cursor *mongo.Cursor, ctx context.Context) {
		_ = cursor.Close(ctx)
	}(cursor, ctx)

	if err = cursor.All(ctx, &organization); err != nil {
		logs.Errorf("fail get Organization cursor.All error: %v", err)
		return nil, CError.ErrorOrgDomainNotExist
	}

	var found bool
	for _, item := range organization {
		if item.LdapConfig != nil && isDomainInLDAPDN(req.DomainName, item.LdapConfig.LdapDN) {
			org = item
			found = true
			break
		}
	}

	// 未找到匹配的域信息
	if !found {
		logs.Errorf("LdapDomainAccountLogin 组织内未找到域信息, DomainName: %s 组织信息 %v", req.DomainName, org)
		return nil, CError.ErrorOrgDomainNotExist
	}
	var userCode string
	userLogin, err := s.userLogin.FindUserByPhoneOrEmailOrLdap(ctx, &dto.SearchUserInfoRequest{
		Account:   req.Account,
		ProductID: req.ProductID,
	})

	if err != nil {
		logs.Errorf("failed to call FindUserByPhoneOrEmailOrLdap error: %s", err)
		return nil, CError.ErrorUserLogin
	}

	if userLogin != nil {
		userCode = userLogin.UserID
		if userLogin.State == 1 {
			logs.PErrorf("用户已被禁用: %s", userLogin.UserID)
			return nil, CError.ErrorUserBlocked
		}
	}

	if userLogin == nil {
		data, err := s.userLogin.LdapUserInfoLogin(ctx, &dto.UserLoginRecordStruct{
			Account:     req.Account,
			ProductID:   req.ProductID,
			ClientID:    req.ClientID,
			LoginMethod: consts.Ldap,
		})

		if err != nil {
			logs.PErrorf("failed to call LdapUserInfoLogin error %s", err)
			return nil, CError.ErrorUserLogin
		}

		if err == nil {
			userCode = data.UserID
		}
	}

	// 查询用户信息
	userFilter := bson.M{
		"product_id":  req.ProductID,
		"org_code":    org.OrgCode,
		"account":     req.Account,
		"source_type": "LDAP",
	}

	user := &dto.UserInfo{}
	if err := s.organizationUserCollection.FindOne(ctx, userFilter).Decode(user); err != nil {
		logs.Errorf("根据用户account %s 和code %s 获取用户信息失败: %v , param: %v", req.Account, req.OrgCode, err, req)
		return nil, CError.ErrorUserNotFound
	}

	if user.Status == 1 {
		logs.PErrorf("用户已被禁用: %s", user.UserCode)
		return nil, CError.ErrorUserBlocked
	}

	// 生成认证用户信息并生成令牌
	authUserInfo := &dto.AuthUserInfo{
		ProductID:   req.ProductID,
		OrgCode:     user.OrgCode,
		UserCode:    userCode,
		Username:    user.Username,
		Email:       user.Email,
		Phone:       user.Phone,
		OrgName:     org.OrgName,
		ClientID:    req.ClientID,
		Account:     req.Account,
		LoginMethod: consts.Ldap,
	}

	return s.generateTokensV2(ctx, authUserInfo)
}

// ValidateTokenV2 验证访问令牌
func (s *authService) ValidateTokenV2(ctx context.Context, accessToken string) (*dto.AuthUserInfo, error) {
	// 解析JWT
	claims, err := utils.ParseJWT(accessToken, s.config.JWT.SecretKey)
	if err != nil {
		logs.PErrorf("解析JWT失败: %v, accessToken: %s, secretKey: %s", err, accessToken, s.config.JWT.SecretKey)
		return nil, errors.New("invalid access token")
	}

	// 确认是访问令牌而非刷新令牌
	if claims.TokenType != "access" {
		logs.PErrorf("令牌类型错误，期望access token，得到: %s", claims.TokenType)
		return nil, errors.New("invalid token type")
	}
	// 检查令牌是否被撤销
	tokenKey := AccessTokenPrefix + claims.ID
	_, err = client.Redis.Get(ctx, tokenKey).Result()
	if err != nil {
		logs.PErrorf("令牌已被撤销: %v", err)
		return nil, errors.New("token revoked")
	}

	// 获取用户信息
	user, err := s.getAuthUserInfo(ctx, claims)
	if err != nil {
		logs.PErrorf("获取用户信息失败: %v", err)
		return nil, errors.New("user not found")
	}

	return user, nil
}

// 获取用户信息
func (s *authService) getAuthUserInfoV2(ctx context.Context, claims *commondto.JWTClaims) (*dto.AuthUserInfo, error) {
	// 从Redis中获取用户信息
	userCodeTokenKey := UserPrefix + claims.UserCode
	userJSON, err := client.Redis.Get(ctx, userCodeTokenKey).Result()
	if err == nil {
		return dto.UnmarshalUserInfo(userJSON)
	}

	// 从数据库中查询用户信息
	var user1 *dto.UserLogin
	user1, err = s.userLogin.FindUserByPhoneOrEmailOrLdap(ctx, &dto.SearchUserInfoRequest{
		ProductID: claims.ProductId,
		UserID:    claims.UserCode,
		Account:   claims.Account,
	})
	if err != nil {
		logs.PErrorf("从数据库中查询用户信息失败: %v", err)
		return nil, errors.New("user not found")
	}

	// 获取组织信息
	org := &dto.Organization{}
	if err := s.organizationCollection.FindOne(ctx, bson.M{"org_code": claims.OrgCode, "product_id": claims.ProductId}).Decode(&org); err != nil {
		logs.Errorf("根据产品id和orgcode查询组织信息失败: %v", err)
	}

	user := &dto.AuthUserInfo{
		ProductID:     claims.ProductId,
		OrgCode:       claims.OrgCode,
		UserCode:      user1.UserID,
		Username:      claims.Username,
		Email:         claims.Email,
		Phone:         claims.Phone,
		OrgName:       org.OrgName,
		PasswordReset: 2,
		LoginMethod:   claims.LoginMethod,
		Account:       claims.Account,
	}

	// 将用户信息存储到Redis中
	userBytes, err := json.Marshal(user)
	if err == nil {
		accessExpiry := 24 * time.Hour // 默认24小时
		confAccessExpiry := s.config.JWT.AccessExpiry
		if confAccessExpiry != 0 {
			// 使用配置中的过期时间
			accessExpiry = time.Duration(confAccessExpiry) * time.Second
		}

		if err := client.Redis.Set(ctx, userCodeTokenKey, string(userBytes), accessExpiry).Err(); err != nil {
			logs.Errorf("存储用户ID 用户信息失败: %v", err)
		}
	}
	return user, nil
}

// RefreshTokenV2 实现刷新令牌
func (s *authService) RefreshTokenV2(ctx context.Context, refreshToken, clientID string) (*dto.TokenResponse, error) {
	// 解析JWT
	claims, err := utils.ParseJWT(refreshToken, s.config.JWT.SecretKey)
	if err != nil {
		logs.PErrorf("解析JWT失败: %v", err)
		return nil, errors.New("invalid refresh token")
	}

	// 确认是刷新令牌而非访问令牌
	if claims.TokenType != "refresh" {
		logs.PErrorf("令牌类型错误，期望refresh token，得到: %s", claims.TokenType)
		return nil, errors.New("invalid token type")
	}

	// 检查刷新令牌是否被撤销
	refreshTokenKey := RefreshTokenPrefix + claims.ID
	_, err = client.Redis.Get(ctx, refreshTokenKey).Result()
	if err != nil {
		logs.PErrorf("刷新令牌已被撤销: %v", err)
		return nil, errors.New("refresh token revoked")
	}

	// 获取用户信息
	user, err := s.getAuthUserInfoV2(ctx, claims)
	if err != nil {
		logs.PErrorf("获取用户信息失败: %v", err)
		return nil, errors.New("user not found")
	}

	// 撤销旧的刷新令牌
	client.Redis.Del(ctx, refreshTokenKey)
	// 撤销旧的访问令牌(设置过期时间为1分钟)
	accessTokenKey := AccessTokenPrefix + claims.ID
	client.Redis.Expire(ctx, accessTokenKey, time.Minute)
	user.ClientID = clientID
	return s.generateTokensV2(ctx, user)
}

func (s *authService) ClearToken(ctx context.Context, req *dto.UserTokenData) error {
	// todo 清redis的数据
	return s.userLogin.ClearUserLoginToken(ctx, req)
}
