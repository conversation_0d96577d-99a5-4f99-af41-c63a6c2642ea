package portal

import (
	"context"
	"errors"
	"fmt"

	"ztna_server_auth/common/logs"
	"ztna_server_auth/dto"
	"ztna_server_auth/infrastructure/client"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

type Service interface {
	FindPortalConfig(ctx context.Context, productId string) (*dto.PortalConfig, error)
	FindPortalConfigBySubDomain(ctx context.Context, subDomain string) (*dto.PortalConfig, error)
}

type portalConfig struct {
	coll *mongo.Collection
}

func NewPortalConfig() Service {
	return &portalConfig{coll: client.Mongo.Database.Collection("apps_portal_config")}
}

func (p *portalConfig) FindPortalConfig(ctx context.Context, productId string) (*dto.PortalConfig, error) {

	var portalConfig = &dto.PortalConfig{}
	err := p.coll.FindOne(ctx, bson.M{"product_id": productId}).Decode(&portalConfig)
	if err != nil {
		if !errors.Is(err, mongo.ErrNoDocuments) {
			logs.Warnf("portalConfig FindOne error %s", err)
			return nil, err
		}
		return nil, mongo.ErrNoDocuments
	}

	portalConfig.Configs.Domain = fmt.Sprintf("https://%s.%s", portalConfig.Configs.SubDomain, portalConfig.Configs.Domain)
	return portalConfig, nil

}

func (p *portalConfig) FindPortalConfigBySubDomain(ctx context.Context, subDomain string) (*dto.PortalConfig, error) {

	var portalConfig = &dto.PortalConfig{}
	err := p.coll.FindOne(ctx, bson.M{"configs.sub_domain": subDomain}).Decode(&portalConfig)
	if err != nil {
		if !errors.Is(err, mongo.ErrNoDocuments) {
			logs.Warnf("portalConfig FindOne error %s", err)
			return nil, err
		}
		return nil, nil
	}
	portalConfig.Configs.Domain = fmt.Sprintf("https://%s.%s", portalConfig.Configs.SubDomain, portalConfig.Configs.Domain)

	return portalConfig, nil

}
