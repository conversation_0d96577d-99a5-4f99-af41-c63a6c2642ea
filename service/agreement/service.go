package agreement

import (
	"context"
	"time"

	"ztna_server_auth/common/logs"
	"ztna_server_auth/dto"
	"ztna_server_auth/infrastructure/client"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type Service interface {
	CheckAgreement(ctx context.Context, productID, userID, userCode, clientID string) error
	GetUserAgreement(ctx context.Context, productID, userID, userCode, clientID string) (*dto.UserAgreement, error)
}

type userAgreement struct {
	coll *mongo.Collection
}

func NewUserAgreement() Service {
	return &userAgreement{
		coll: client.Mongo.Database.Collection("users_agreement"),
	}
}

func (u *userAgreement) CheckAgreement(ctx context.Context, productId, userID, userCode, clientID string) error {

	filter := bson.D{
		{"product_id", productId},
		{"user_id", userID},
		{"client_id", clientID}}

	updateValue := bson.D{
		{"$set", bson.D{
			{"updated_at", time.Now().Unix()},
		}},
		{"$setOnInsert", bson.D{
			{"created_at", time.Now().Unix()},
			{"checked", 1},
			{"agreement_id", "user_usage"},
		}},
	}

	err := u.coll.FindOneAndUpdate(ctx, filter, updateValue, options.FindOneAndUpdate().SetUpsert(true)).Err()
	if err != nil {
		logs.Warnf("failed to call FindOneAndUpdate error: %s", err)

	}

	return err

}

func (u *userAgreement) GetUserAgreement(ctx context.Context, productID, userID, userCode, clientID string) (*dto.UserAgreement, error) {

	var userA = &dto.UserAgreement{}
	if err := u.coll.FindOne(ctx,
		bson.D{{"product_id", productID},
			{"user_id", userID},
			{"client_id", clientID},
		}).Decode(&userA); err != nil {
		logs.PErrorf("FAILED to coll Data error: %s", err)
		return nil, err
	}

	return userA, nil

}
