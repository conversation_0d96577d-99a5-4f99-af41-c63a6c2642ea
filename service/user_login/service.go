package user_login

import (
	"context"
	"errors"
	"time"

	"ztna_server_auth/common/consts"
	"ztna_server_auth/common/logs"
	"ztna_server_auth/common/utils"
	"ztna_server_auth/dto"
	"ztna_server_auth/infrastructure/client"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type Service interface {
	RecordUserLoginData(ctx context.Context, req *dto.UserLoginRecordStruct) (string, error)
	CheckToken(ctx context.Context, req *dto.UserTokenData) error
	FindUserByPhoneOrEmailOrLdap(ctx context.Context, req *dto.SearchUserInfoRequest) (*dto.UserLogin, error)
	FindAndInsert(ctx context.Context, req *dto.UserLoginRecordStruct) (*dto.UserLogin, error)
	LdapUserInfoLogin(ctx context.Context, req *dto.UserLoginRecordStruct) (*dto.UserLogin, error)

	FindUserLoginToken(ctx context.Context, req *dto.UserTokenData) error
	ClearUserLoginToken(ctx context.Context, req *dto.UserTokenData) error
}

type userLogin struct {
	usersColl, userToken, userLoginColl, organizationUserCollection *mongo.Collection
}

func NewUserLogin() Service {

	return &userLogin{
		usersColl:                  client.Mongo.Database.Collection("users"),
		userToken:                  client.Mongo.Database.Collection("users_token"),
		userLoginColl:              client.Mongo.Database.Collection("users_login"),
		organizationUserCollection: client.Mongo.Database.Collection("organization_user"),
	}

}

func (l *userLogin) RecordUserLoginData(ctx context.Context, req *dto.UserLoginRecordStruct) (string, error) {

	if req.Phone == "" && req.Email == "" {
		logs.Warnf("this login user iphone and email is empty")
		return "", errors.New("req.email or req.Phone is empty")
	}

	orgUsers, err := l.getOrgUserInfo(ctx, req.Phone, req.Email, req.ProductID)
	if err != nil {
		logs.Warnf("getOrgUserInfo failed: %v", err)
		return "", err
	}

	if len(orgUsers) == 0 {
		logs.Warnf("orgUsers is empty")
		return "", errors.New("orgUsers is empty")
	}

	var userLogin = make([]*dto.UserLogin, 0, len(orgUsers))

	// 处理数据
	userId, _ := utils.Sequence.GenerateUserCode()

	for _, orgUser := range orgUsers {
		if orgUser.Mobile != "" {
			userLogin = append(userLogin, &dto.UserLogin{
				LoginMethod: req.LoginMethod,
				UserID:      userId,
				ProductID:   orgUser.ProductID,
				LoginValue:  orgUser.Mobile,
				LoginType:   dto.TypeOfPhone,
				State:       0,
			})
		}
		if orgUser.Email != "" {
			userLogin = append(userLogin, &dto.UserLogin{
				UserID:      userId,
				LoginMethod: req.LoginMethod,
				ProductID:   orgUser.ProductID,
				LoginValue:  orgUser.Email,
				LoginType:   dto.TypeOfEmail,
				State:       1,
			})
		}

	}

	insertLoginErr := l.insertUserLoginData(ctx, userLogin)
	if insertLoginErr != nil {
		logs.Warnf("failed to call insertUserLoginData error: %s", insertLoginErr)
		return "", insertLoginErr
	}
	userName := orgUsers[0].UserName

	userInfo, err := l.InsertOrUpdateData(ctx, &dto.Users{
		UserID:    userId,
		Username:  userName,
		ProductID: req.ProductID,
	})

	if err != nil {
		logs.Warnf("failed to call InsertOrUpdateData %s", err)
		return "", err
	}

	logs.Infof("userInfo: %v", userInfo)
	if req.Token != "" {
		InsertUserTokenErr := l.InsertOrUpdateUserToken(ctx, &dto.UserTokenData{
			UserID:    userInfo.UserID,
			ProductID: req.ProductID,
			ClientID:  req.ClientID,
			Token:     req.Token,
			ExpiredAt: time.Now().Add(time.Duration(req.ExpiatedAt) * time.Second),
		})
		if InsertUserTokenErr != nil {
			logs.Warnf("failed to call InsertOrUpdateUserToken error: %s", InsertUserTokenErr)
		}
	}

	// 返回生成的 user_id
	logs.Infof("RecordUserLoginData completed, returning user_id: %s", userId)
	return userInfo.UserID, nil

}

func (l *userLogin) getOrgUserInfo(ctx context.Context, phone, email, productID string) ([]*dto.OrganizationUser, error) {

	var orgUsers = make([]*dto.OrganizationUser, 0)

	filter := bson.M{"product_id": productID}

	if phone != "" {
		filter["mobile"] = phone
	}
	if email != "" {
		filter["email"] = email
	}

	cursor, err := l.organizationUserCollection.Find(ctx, filter)
	if err != nil {
		logs.Warnf("failed to call Find orgUser filter: %+v , error: %s", filter, err)
		return nil, err
	}

	if decodeErr := cursor.All(ctx, &orgUsers); decodeErr != nil {
		logs.Warnf("failed to call All Decode error %s", err)
		return nil, decodeErr
	}

	return orgUsers, nil

}

func (l *userLogin) InsertOrUpdateData(ctx context.Context, req *dto.Users) (*dto.Users, error) {

	var baseUserInfo = &dto.Users{}

	filter := bson.D{
		{"user_id", req.UserID},
		{"product_id", req.ProductID},
	}

	updateValue := bson.D{
		{"$set", bson.D{
			{"updated_at", time.Now().Unix()},
			{"username", req.Username},
		}},
		{"$setOnInsert", bson.D{
			{"created_at", time.Now().Unix()},
		}},
	}
	if err := l.usersColl.FindOneAndUpdate(ctx, filter, updateValue, options.FindOneAndUpdate().SetUpsert(true).SetReturnDocument(options.After)).Decode(&baseUserInfo); err != nil {
		logs.Warnf("failed to FindOneAndUpdate error %s,filter: %+v, update: %+v", err, filter, updateValue)
		return nil, err
	}

	return baseUserInfo, nil

}

func (l *userLogin) insertUserLoginData(ctx context.Context, req []*dto.UserLogin) error {

	var wms = make([]mongo.WriteModel, 0, len(req))

	for _, value := range req {

		wms = append(wms, mongo.NewUpdateOneModel().
			SetFilter(bson.D{
				{"login_value", value.LoginValue},
				{"login_type", value.LoginType},
				{"product_id", value.ProductID},
			}).
			SetUpdate(bson.D{
				{"$set", bson.D{
					{"updated_at", time.Now().Unix()},
					{"state", value.State},
					{"source_type", value.SourceType},
					{"login_method", value.LoginMethod},
				}},
				{"$setOnInsert", bson.D{
					{"created_at", time.Now().Unix()},
					{"user_id", value.UserID},
				}},
			}).SetUpsert(true),
		)

	}

	bulkWrite, err := l.userLoginColl.BulkWrite(ctx, wms, options.BulkWrite())
	if err != nil {
		logs.Warnf("failed to BulkWrite error %s", err)

	}

	logs.Infof("bulkWrite %+v", bulkWrite)

	return err

}

func (l *userLogin) userLoginRelation(ctx context.Context, value *dto.UserLogin) (*dto.UserLogin, error) {

	filter := bson.D{
		{"login_value", value.LoginValue},
		{"login_type", value.LoginType},
		{"product_id", value.ProductID},
	}

	updateValue := bson.D{
		{"$set", bson.D{
			{"updated_at", time.Now().Unix()},
			{"state", value.State},
			{"source_type", value.SourceType},
			{"login_method", value.LoginMethod},
		}},
		{"$setOnInsert", bson.D{
			{"created_at", time.Now().Unix()},
			{"user_id", value.UserID},
		}},
	}

	var loginRelation = &dto.UserLogin{}

	if err := l.userLoginColl.FindOneAndUpdate(ctx, filter, updateValue,
		options.FindOneAndUpdate().SetUpsert(true).SetReturnDocument(options.After)).Decode(&loginRelation); err != nil {
		return nil, err
	}

	return loginRelation, nil

}

func (l *userLogin) InsertOrUpdateUserToken(ctx context.Context, req *dto.UserTokenData) error {

	filter := bson.D{
		{"user_id", req.UserID},
		{"client_id", req.ClientID},
		{"product_id", req.ProductID},
	}

	updateValue := bson.D{
		{"$set", bson.D{
			{"updated_at", time.Now().Unix()},
			{"token", req.Token},
			{"expired_at", req.ExpiredAt},
		}},
		{"$setOnInsert", bson.D{
			{"created_at", time.Now().Unix()},
		}},
	}

	_, err := l.userToken.UpdateOne(ctx, filter, updateValue, options.Update().SetUpsert(true))
	if err != nil {
		logs.Warnf("failed to UpdateOne error %s", err)
	}

	return nil

}

func (l *userLogin) CheckToken(ctx context.Context, req *dto.UserTokenData) error {

	filter := bson.M{
		"user_id":    req.UserID,
		"product_id": req.ProductID,
		"client_id":  req.ClientID,
	}

	now := time.Now()

	update := bson.M{
		"$set": bson.M{
			"updated_at": now.Unix(),
			"token":      req.Token,
			"expired_at": req.ExpiredAt,
		},
		"$setOnInsert": bson.M{
			"created_at": now.Unix(),
		},
	}

	userToken := &dto.UserTokenData{}

	err := l.userToken.FindOneAndUpdate(ctx, filter, update, options.FindOneAndUpdate().SetUpsert(true).SetReturnDocument(options.After)).Decode(&userToken)
	if err != nil {
		logs.Warnf("failed to Find userToken error %s", err)
	}
	return err

}

func (l *userLogin) FindUserByPhoneOrEmailOrLdap(ctx context.Context, req *dto.SearchUserInfoRequest) (*dto.UserLogin, error) {

	filter := bson.M{"product_id": req.ProductID}

	if req.Email != "" {
		filter["login_value"] = req.Email
		filter["login_type"] = dto.TypeOfEmail
	} else if req.Phone != "" {
		filter["login_value"] = req.Phone
		filter["login_type"] = dto.TypeOfPhone
	} else if req.Account != "" {
		filter["login_value"] = req.Account
		filter["login_type"] = dto.TypeOfLdap
	}

	if req.UserID != "" {
		filter["user_id"] = req.UserID
	}

	var userInfo = &dto.UserLogin{}
	err := l.userLoginColl.FindOne(ctx, filter, options.FindOne().SetSort(bson.D{{"updated_at", 1}})).Decode(&userInfo)
	if err != nil {
		logs.Warnf("failed to Find userInfo error %s", err)

		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}
		return nil, err
	}

	return userInfo, nil

}

func (l *userLogin) FindAndInsert(ctx context.Context, req *dto.UserLoginRecordStruct) (*dto.UserLogin, error) {
	//
	if req.Phone == "" && req.Email == "" {
		logs.Warnf("this login user iphone and email is empty")
		return nil, errors.New("req.email or req.Phone is empty")
	}

	orgUsers, err := l.getOrgUserInfo(ctx, req.Phone, req.Email, req.ProductID)
	if err != nil {
		logs.Warnf("getOrgUserInfo failed: %v", err)
		return nil, err
	}

	if len(orgUsers) == 0 {
		logs.Warnf("orgUsers is empty")
		return nil, errors.New("orgUsers is empty")
	}

	uId, _ := utils.Sequence.GenerateUserCode()
	var state int32

	phoneSet := utils.NewSet[string]()
	emailSet := utils.NewSet[string]()
	for _, user := range orgUsers {
		if user.Mobile != "" {
			phoneSet.Add(user.Mobile)
		}
		if user.Email != "" {
			emailSet.Add(user.Email)
		}

		if user.Status == 1 {
			state = 1
		}

	}

	if phoneSet.Size() > 0 || emailSet.Size() > 0 {

		//查询 是否有数据了
		var decodeUser = &dto.UserLogin{}
		if req.LoginMethod == consts.EmailLogin {
			if err := l.userLoginColl.FindOne(ctx,
				bson.D{{"product_id", req.ProductID},
					{"login_type", 0},
					{"login_value", bson.D{{"$in", phoneSet.ToSlice()}}}}).Decode(&decodeUser); err == nil {

				uId = decodeUser.UserID
			}
		}

		if req.LoginMethod == consts.PhoneLogin {
			if err := l.userLoginColl.FindOne(ctx,
				bson.D{{"product_id", req.ProductID},
					{"login_type", 1},
					{"login_value", bson.D{{"$in", emailSet.ToSlice()}}}}).Decode(&decodeUser); err == nil {

				uId = decodeUser.UserID
			}
		}

	}

	if req.LoginMethod == consts.PhoneLogin {
		for _, orgUser := range orgUsers {
			if orgUser.Status == 1 {
				state = 1
			}
		}
	}

	data := &dto.UserLogin{
		UserID:      uId,
		ProductID:   req.ProductID,
		State:       state,
		LoginMethod: req.LoginMethod,
		SourceType:  orgUsers[0].SourceType,
	}
	if req.LoginMethod == consts.PhoneLogin {
		data.LoginMethod = req.LoginMethod
		data.LoginType = 0
		data.LoginValue = req.Phone
	}
	if req.LoginMethod == consts.EmailLogin {
		data.LoginMethod = req.LoginMethod
		data.LoginType = 1
		data.LoginValue = req.Email
	}

	userLoginInfo, err := l.userLoginRelation(ctx, data)
	if err != nil {
		logs.Warnf("failed to call userLoginRelation error %s", err)
		return nil, err
	}

	_, err = l.InsertOrUpdateData(ctx, &dto.Users{
		UserID:    userLoginInfo.UserID,
		Username:  orgUsers[0].UserName,
		ProductID: userLoginInfo.ProductID,
	})

	if err != nil {
		logs.Warnf("failed to call InsertOrUpdateData %s", err)
		return nil, err
	}

	return userLoginInfo, nil

}

func (l *userLogin) LdapUserInfoLogin(ctx context.Context, req *dto.UserLoginRecordStruct) (*dto.UserLogin, error) {
	var orgUsers = &dto.OrganizationUser{}
	if err := l.organizationUserCollection.FindOne(ctx,
		bson.M{"product_id": req.ProductID,
			"account":     req.Account,
			"source_type": "LDAP"}).Decode(&orgUsers); err != nil {
		logs.Warnf("failed to call FindOne error %s", err)

	}
	uID, _ := utils.Sequence.GenerateUserCode()
	userInfo, err := l.userLoginRelation(ctx, &dto.UserLogin{
		UserID:      uID,
		ProductID:   req.ProductID,
		LoginValue:  req.Account,
		LoginType:   2,
		SourceType:  "LDAP",
		State:       int32(orgUsers.Status),
		LoginMethod: consts.Ldap,
	})
	if err != nil {
		logs.Warnf("failed to call userLoginRelation error %s", err)
		return nil, err

	}

	_, err = l.InsertOrUpdateData(ctx, &dto.Users{
		UserID:    userInfo.UserID,
		Username:  orgUsers.UserName,
		ProductID: req.ProductID,
	})
	if err != nil {
		logs.Warnf("failed to call InsertOrUpdateData %s", err)
		return nil, err
	}
	return userInfo, nil

}

// ClearUserLoginToken  删除用户的token
func (l *userLogin) ClearUserLoginToken(ctx context.Context, req *dto.UserTokenData) error {

	err := l.userToken.FindOneAndDelete(ctx,
		bson.D{{"product_id", req.ProductID},
			{"user_id", req.UserID},
			{"client_id", req.ClientID}}).Err()
	if err != nil {
		logs.Warnf("failed to  Delete userToken error %s", err)

	}

	return nil
}

func (l *userLogin) FindUserLoginToken(ctx context.Context, req *dto.UserTokenData) error {

	err := l.userToken.FindOne(ctx,
		bson.D{{"product_id", req.ProductID},
			{"user_id", req.UserID},
			{"client_id", req.ClientID}}).Err()
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			logs.Warnf("failed to find this userToken error %s", err)
		}
		logs.Warnf("find this userToken had an error %s", err)
		return err
	}

	return nil

}
