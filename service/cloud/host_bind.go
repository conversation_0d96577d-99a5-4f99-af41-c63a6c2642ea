package cloud

import (
	"context"

	"ztna_server_auth/common/logs"
	"ztna_server_auth/dto"
	"ztna_server_auth/infrastructure/client"

	infoGrpc "rm.git/cloud_api/rm_common_protos.git/proto_go/info_v1"
)

type Service interface {
	BindHostInfoData(ctx context.Context, req *dto.BindHostInfoDataRequest) error
}
type hostBind struct {
}

func NewHostBindService() Service {
	return &hostBind{}
}

func (h *hostBind) BindHostInfoData(ctx context.Context, req *dto.BindHostInfoDataRequest) error {
	resp, err := client.InfoGrpcClient.ReportDeviceAssetBindInfoOnLogin(ctx, &infoGrpc.DeviceAssetBindInfoRequest{
		OrgName:      req.OrgName,
		ClientId:     req.ClientId,
		UserCode:     req.UserCode,
		RegisterName: req.UserName,
	})
	if err != nil {
		logs.Errorf("report device asset bind info on login failed: %v", err)

	}
	logs.Info("report device asset bind info on login success: %v", resp)
	return err
}
