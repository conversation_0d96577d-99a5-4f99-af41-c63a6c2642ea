package clients

//
//func NewMongodb(conifAddr, userName, password, database, option string, isDirect bool) (*mongo.Client, error) {
//	uri := fmt.Sprintf("mongodb://%s:%s@%s/%s%s", userName, password, conifAddr, database, option)
//
//	opt := options.Client()
//
//	if isDirect {
//		opt = opt.SetDirect(isDirect)
//	}
//
//	opt = opt.ApplyURI(uri)
//
//	client, err := mongo.Connect(context.Background(), opt)
//	if err != nil {
//		return nil, fmt.Errorf("connect to mongodb failed, %v", err)
//	}
//
//	if err = client.Ping(context.Background(), nil); err != nil {
//		return nil, fmt.Errorf("ping mongodb failed, %v", err)
//	}
//
//	return client, nil
//}
