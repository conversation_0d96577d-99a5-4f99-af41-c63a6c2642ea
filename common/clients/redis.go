package clients

//
//type RedisClient interface {
//	redis.Cmdable
//	Close() error
//	Ping(ctx context.Context) *redis.StatusCmd
//	Watch(ctx context.Context, fn func(*redis.Tx) error, keys ...string) error
//}
//
//var (
//	redisClients = make(map[string]RedisClient)
//)
//
//// NewRedis NewRedis函数用于创建一个Redis客户端实例。
//// 参数：
////   - config: Redis配置
////
//// 返回值：
////   - interfaces.RedisClient: Redis客户端接口，用于操作Redis实例。
////   - error: 错误信息，如果创建Redis客户端失败则返回错误。
//func NewRedis(connMode int8, address []string, password string, db int) (RedisClient, error) {
//	mode := ""
//
//	if connMode == 0 {
//		mode = "单机模式"
//	} else if connMode == 1 {
//		mode = "集群模式"
//	}
//
//	if client, ok := redisClients[getRedisClientKey(connMode, address)]; ok {
//		fmt.Printf("\nredis config 连接模式：%s\n", mode)
//		return client, nil
//	}
//
//	var client RedisClient
//
//	if connMode == 0 {
//		client = redis.NewClient(&redis.Options{
//			Addr:     address[0],
//			Password: password,
//			DB:       db},
//		)
//	} else if connMode == 1 {
//		client = redis.NewClusterClient(&redis.ClusterOptions{
//			Addrs:           address,
//			Password:        password,
//			PoolSize:        40,
//			MinIdleConns:    10,
//			DialTimeout:     5 * time.Second,
//			ConnMaxIdleTime: 5 * time.Second,
//		})
//	}
//
//	if err := client.Ping(context.TODO()).Err(); err != nil {
//		return nil, fmt.Errorf("connect to redis failed, %v", err)
//	}
//
//	fmt.Printf("\nredis config 连接模式：%s\n", mode)
//	redisClients[getRedisClientKey(connMode, address)] = client
//	return client, nil
//}
//
//func getRedisClientKey(connectionMode int8, addr []string) string {
//	return Md5(fmt.Sprintf("%d:%s", connectionMode, strings.Join(addr, ",")))
//}
//
//func Md5(str string) string {
//	m := md5.New()
//	m.Write([]byte(str))
//	return fmt.Sprintf("%x", m.Sum(nil))
//}
