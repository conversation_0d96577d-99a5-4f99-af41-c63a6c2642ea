package logs

import (
	"fmt"
)

var (
	logHandler *Logs
)

func Init(conf *Config) {
	if logHandler == nil {
		logHandler = New(conf)
	}
}

func Info(args ...interface{}) {
	logHandler.Info(fmt.Sprint(args...))
}

func Infof(format string, args ...interface{}) {
	logHandler.Info(fmt.Sprintf(format, args...))
}

func Warn(args ...interface{}) {
	logHandler.Warn(fmt.Sprint(args...))
}

func Warnf(format string, args ...interface{}) {
	logHandler.Warn(fmt.Sprintf(format, args...))
}

func Error(args ...interface{}) {
	logHandler.Error(fmt.Sprint(args...))
}

func Errorf(format string, args ...interface{}) {
	logHandler.Error(fmt.Sprintf(format, args...))
}

func Fatal(args ...interface{}) {
	logHandler.Fatal(fmt.Sprint(args...))
}

func Fatalf(format string, args ...interface{}) {
	logHandler.Fatalf(fmt.Sprintf(format, args...))
}

func Trace(args ...interface{}) {
	logHandler.Trace(fmt.Sprint(args...))
}

func Tracef(format string, args ...interface{}) {
	logHandler.Tracef(fmt.Sprintf(format, args...))
}

func Debug(args ...interface{}) {
	logHandler.Debug(fmt.Sprint(args...))
}

func Debugf(format string, args ...interface{}) {
	logHandler.Debug(fmt.Sprintf(format, args...))
}

func PInfo(args ...interface{}) {
	logHandler.PInfo(fmt.Sprint(args...))
}

func PInfof(format string, args ...interface{}) {
	logHandler.PInfo(fmt.Sprintf(format, args...))
}

func PWarn(args ...interface{}) {
	logHandler.PWarn(fmt.Sprint(args...))
}

func PWarnf(format string, args ...interface{}) {
	logHandler.PWarn(fmt.Sprintf(format, args...))
}

func PError(args ...interface{}) {
	logHandler.PError(fmt.Sprint(args...))
}

func PErrorf(format string, args ...interface{}) {
	logHandler.PError(fmt.Sprintf(format, args...))
}

func PFatal(args ...interface{}) {
	logHandler.PFatal(fmt.Sprint(args...))
}

func PFatalf(format string, args ...interface{}) {
	logHandler.PFatalf(fmt.Sprintf(format, args...))
}

func PTrace(args ...interface{}) {
	logHandler.PTrace(fmt.Sprint(args...))
}

func PTracef(format string, args ...interface{}) {
	logHandler.PTracef(fmt.Sprintf(format, args...))
}

func PDebug(args ...interface{}) {
	logHandler.PDebug(fmt.Sprint(args...))
}

func PDebugf(format string, args ...interface{}) {
	logHandler.PDebug(fmt.Sprintf(format, args...))
}
