package logs

import (
	"context"
	"fmt"
	"io"
	"log/slog"
	"os"
	"strings"
	"time"

	rotatelogs "github.com/lestrrat-go/file-rotatelogs"
)

type Logs struct {
	conf   *Config
	logger *slog.Logger
}

type Config struct {
	Stdout        bool
	Dir           string
	Type          string
	Level         string
	RotationTime  time.Duration
	RotationCount uint
}

func NameToLevel(name string) slog.Level {

	levelMappings := map[string]slog.Level{
		"fatal": slog.LevelError + 4,
		"error": slog.LevelError,
		"warn":  slog.LevelWarn,
		"info":  slog.LevelInfo,
		"debug": slog.LevelDebug,
		"trace": slog.LevelDebug - 4,
	}
	name = strings.ToLower(name)
	if level, ok := levelMappings[name]; ok {
		return level
	}
	return slog.LevelInfo
}

func DefaultConfig() *Config {
	return &Config{
		Stdout:        true,
		Dir:           "log",
		Type:          "json",
		Level:         "info",
		RotationTime:  time.Hour * 24,
		RotationCount: 3,
	}
}

func New(conf *Config) *Logs {

	logfileName := fmt.Sprintf("%s/%%Y%%m%%d.log", conf.Dir)
	rotateLog, _ := rotatelogs.New(
		logfileName,
		rotatelogs.WithRotationTime(conf.RotationTime),
		rotatelogs.WithRotationCount(conf.RotationCount),
	)
	var logger *slog.Logger
	switch conf.Type {
	case "text":
		logger = slog.New(
			slog.NewTextHandler(
				io.MultiWriter(os.Stdout, rotateLog),
				&slog.HandlerOptions{
					Level: NameToLevel(conf.Level),
				},
			),
		)
	case "json":
		logger = slog.New(
			slog.NewJSONHandler(
				io.MultiWriter(os.Stdout, rotateLog),
				&slog.HandlerOptions{
					Level: NameToLevel(conf.Level),
				},
			),
		)
	default:
		logger = slog.New(
			slog.NewTextHandler(
				io.MultiWriter(os.Stdout, rotateLog),
				&slog.HandlerOptions{
					Level: NameToLevel(conf.Level),
				},
			),
		)
	}
	return &Logs{logger: logger, conf: conf}
}

func (l *Logs) WithContext(ctx context.Context, key string) *Logs {
	return &Logs{
		logger: l.logger.With(key, ctx.Value(key)),
	}
}

func (l *Logs) Info(args ...interface{}) {
	l.logger.Info(fmt.Sprint(args...))
}

func (l *Logs) PInfo(args ...interface{}) {
	if NameToLevel(l.conf.Level) <= NameToLevel("info") {
		fmt.Println("\033[5;32m", time.Now(), fmt.Sprint(args...), "\033[0m")
	}
}

func (l *Logs) Infof(format string, args ...interface{}) {
	l.logger.Info(fmt.Sprintf(format, args...))
}

func (l *Logs) PInfof(format string, args ...interface{}) {
	if NameToLevel(l.conf.Level) <= NameToLevel("info") {
		fmt.Println("\033[5;32m", time.Now(), fmt.Sprintf(format, args...), "\033[0m")
	}
}

func (l *Logs) Warn(args ...interface{}) {
	l.logger.Warn(fmt.Sprint(args...))
}

func (l *Logs) PWarn(args ...interface{}) {
	if NameToLevel(l.conf.Level) <= NameToLevel("warn") {
		fmt.Println("\033[5;33m", time.Now(), fmt.Sprint(args...), "\033[0m")
	}
}

func (l *Logs) Warnf(format string, args ...interface{}) {
	l.logger.Warn(fmt.Sprintf(format, args...))
}

func (l *Logs) PWarnf(format string, args ...interface{}) {
	if NameToLevel(l.conf.Level) <= NameToLevel("warn") {
		fmt.Println("\033[5;33m", time.Now(), fmt.Sprintf(format, args...), "\033[0m")
	}
}

func (l *Logs) Error(args ...interface{}) {
	l.logger.Error(fmt.Sprint(args...))
}

func (l *Logs) PError(args ...interface{}) {
	if NameToLevel(l.conf.Level) <= NameToLevel("error") {
		fmt.Println("\033[5;31m", time.Now(), fmt.Sprint(args...), "\033[0m")
	}
}

func (l *Logs) Errorf(format string, args ...interface{}) {
	l.logger.Error(fmt.Sprintf(format, args...))
}

func (l *Logs) PErrorf(format string, args ...interface{}) {
	if NameToLevel(l.conf.Level) <= NameToLevel("error") {
		fmt.Println("\033[5;31m", time.Now(), fmt.Sprintf(format, args...), "\033[0m")
	}
}

func (l *Logs) Fatal(args ...interface{}) {
	l.logger.Log(context.Background(), slog.Level(12), fmt.Sprint(args...))
}

func (l *Logs) PFatal(args ...interface{}) {
	if NameToLevel(l.conf.Level) <= NameToLevel("fatal") {
		fmt.Println("\033[5;34m", time.Now(), fmt.Sprint(args...), "\033[0m")
	}
}

func (l *Logs) Fatalf(format string, args ...interface{}) {
	l.logger.Log(context.Background(), slog.Level(12), fmt.Sprintf(format, args...))
}

func (l *Logs) PFatalf(format string, args ...interface{}) {
	if NameToLevel(l.conf.Level) <= NameToLevel("fatal") {
		fmt.Println("\033[5;34m", time.Now(), fmt.Sprintf(format, args...), "\033[0m")
	}
}

func (l *Logs) Trace(args ...interface{}) {
	l.logger.Log(context.Background(), slog.Level(-8), fmt.Sprint(args...))
}

func (l *Logs) PTrace(args ...interface{}) {
	if NameToLevel(l.conf.Level) <= NameToLevel("trace") {
		fmt.Println("\033[5;35m", time.Now(), fmt.Sprint(args...), "\033[0m")
	}
}

func (l *Logs) Tracef(format string, args ...interface{}) {
	l.logger.Log(context.Background(), slog.Level(-8), fmt.Sprintf(format, args...))
}

func (l *Logs) PTracef(format string, args ...interface{}) {
	if NameToLevel(l.conf.Level) <= NameToLevel("trace") {
		fmt.Println("\033[5;35m", time.Now(), fmt.Sprintf(format, args...), "\033[0m")
	}
}

func (l *Logs) Debug(args ...interface{}) {
	l.logger.Debug(fmt.Sprint(args...))
}

func (l *Logs) PDebug(args ...interface{}) {
	if NameToLevel(l.conf.Level) <= NameToLevel("debug") {
		fmt.Println("\033[5;36m", time.Now(), fmt.Sprint(args...), "\033[0m")
	}
}

func (l *Logs) Debugf(format string, args ...interface{}) {
	l.logger.Debug(fmt.Sprintf(format, args...))
}

func (l *Logs) PDebugf(format string, args ...interface{}) {
	if NameToLevel(l.conf.Level) <= NameToLevel("debug") {
		fmt.Println("\033[5;36m", time.Now(), fmt.Sprintf(format, args...), "\033[0m")
	}
}
