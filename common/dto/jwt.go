package common_dto

import (
	"github.com/golang-jwt/jwt/v5"
)

// JWTClaims 定义JWT载荷结构
type JWTClaims struct {
	ProductId   string   `json:"product_id"`
	OrgCode     string   `json:"org_code"`
	UserCode    string   `json:"user_code"`
	Username    string   `json:"username,omitempty"`
	Email       string   `json:"email,omitempty"`
	Roles       []string `json:"roles,omitempty"`
	Phone       string   `json:"phone,omitempty"`
	Account     string   `json:"account,omitempty"`
	LoginMethod string   `json:"login_method,omitempty"`
	TokenType   string   `json:"token_type"` // "access" 或 "refresh"
	jwt.RegisteredClaims
}

// JWTConfig JWT配置
type JWTConfig struct {
	SecretKey     string `mapstructure:"secret_key"`
	AccessExpiry  int    `mapstructure:"access_expiry"`
	RefreshExpiry int    `mapstructure:"refresh_expiry"`
	Issuer        string `mapstructure:"issuer"`
}
