package rmemail

import (
	"testing"
)

func TestNewRmEmail(t *testing.T) {
	// 创建测试配置
	config := RmEmailConfig{
		Port:      587,
		Host:      "smtp.example.com",
		User:      "<EMAIL>",
		Password:  "password123",
		AliasName: "Test User",
	}

	// 创建RmEmail实例
	email := NewRmEmail(config)

	// 发送邮件
	err := email.SendEmail("test", "test", "test", []string{"<EMAIL>"}, []string{}, []string{}, []RmEmailAttach{}...)
	if err != nil {
		t.<PERSON>rrorf("发送邮件失败: %v", err)
	}

	t.Log("邮件发送成功")
}
