package rmemail

import (
	"gopkg.in/gomail.v2"
)

type RmEmailConfig struct {
	Port      int
	Host      string
	User      string
	Password  string
	AliasName string
}

type RmEmailAttach struct {
	FileName    string
	FileSetting gomail.FileSetting
}

type RmEmail struct {
	emailConfig RmEmailConfig
}

func NewRmEmail(emailConfig RmEmailConfig) *RmEmail {
	return &RmEmail{
		emailConfig: emailConfig,
	}
}

// SendEmail 发送电子邮件
// 参数:
//   - content: 邮件正文内容
//   - subject: 邮件主题
//   - nameAlias: 发件人别名
//   - to: 收件人邮箱地址列表
//   - cc: 抄送人邮箱地址列表
//   - bcc: 密送人邮箱地址列表
//   - attach: 邮件附件列表(可选)
//
// 返回值:
//   - error: 发送失败时返回错误信息
func (r *RmEmail) SendEmail(content, subject, aliasName string, to, cc, bcc []string, attach ...RmEmailAttach) error {
	port := r.emailConfig.Port
	host := r.emailConfig.Host
	user := r.emailConfig.User
	password := r.emailConfig.Password
	if aliasName == "" {
		aliasName = r.emailConfig.AliasName
	}

	m := gomail.NewMessage()
	m.SetAddressHeader("From", user, aliasName)
	m.SetHeader("To", to...)
	if len(cc) > 0 {
		m.SetHeader("Cc", cc...)
	}
	if len(bcc) > 0 {
		m.SetHeader("Bcc", bcc...)
	}
	m.SetHeader("Subject", subject)

	m.SetBody("text/html", content)

	if len(attach) > 0 {
		for _, a := range attach {
			m.Attach(a.FileName, a.FileSetting)
		}
	}

	d := gomail.NewDialer(host, port, user, password)

	if err := d.DialAndSend(m); err != nil {
		return err
	}
	return nil
}
