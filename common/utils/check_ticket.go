package utils

import (
	"bytes"
	"encoding/json"
	baseError "errors"
	"fmt"
	"io"
	"net/http"

	"ztna_server_auth/common/config"
	"ztna_server_auth/common/errors"
	"ztna_server_auth/common/logs"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	captcha "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/captcha/v20190722"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	tencentError "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/errors"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"
)

var conf = config.Config()

// VerifyImageCode 验证图片验证码
func VerifyImageCode(ctx *gin.Context, ticket, random string) error {

	if ticket == "" || random == "" {
		return errors.ErrSmsSend
	}

	credential := common.NewCredential(
		conf.Sms.SecretId,
		conf.Sms.SecretKey,
	)

	cpf := profile.NewClientProfile()
	cpf.HttpProfile.Endpoint = "captcha.tencentcloudapi.com"

	client, _ := captcha.NewClient(credential, "", cpf)

	request := captcha.NewDescribeCaptchaResultRequest()
	request.CaptchaType = common.Uint64Ptr(9)
	request.Ticket = common.StringPtr(ticket)
	request.UserIp = common.StringPtr(GetClientIp(ctx))
	request.Randstr = common.StringPtr(random)
	request.CaptchaAppId = common.Uint64Ptr(conf.Sms.CaptchaAppid)
	request.AppSecretKey = common.StringPtr(conf.Sms.CaptchaAppSecretKey)

	// 返回的resp是一个DescribeCaptchaResultResponse的实例，与请求对象对应
	response, err := client.DescribeCaptchaResult(request)
	var tencentCloudSDKError *tencentError.TencentCloudSDKError
	if baseError.As(err, &tencentCloudSDKError) {
		logs.Errorf("DescribeCaptchaResultRequest error: %s", err)
		return err
	}

	if err != nil {
		logs.Errorf("DescribeCaptchaResultRequest error: %s", err)
		return err
	}

	if *response.Response.CaptchaCode != 1 {
		return errors.ErrSmsSend
	}

	return nil
}

type ValidateCodeSend struct {
	NotifyTarget  string `json:"notify_target"`
	SendEmailPure struct {
		Content string   `json:"content"`
		Emails  []string `json:"emails"`
		Subject string   `json:"subject"`
	} `json:"send_email_pure"`
	SendSmsPure struct {
		Content   string   `json:"content"`
		Phones    []string `json:"phones"`
		TplID     string   `json:"tpl_id"`
		TplParams []string `json:"tpl_params"`
	} `json:"send_sms_pure"`
	TplType string `json:"tpl_type"`
}

// SendMessage 发送短信的方法 phone || tid 模板ID || 验证码
func SendMessage(phone string, tid string, verificationCode string) error {

	//验证码有效时长
	codeValidTime := cast.ToString(conf.Sms.VerificationCodeValidTime)
	tParamSet := []string{verificationCode, codeValidTime}

	template := conf.Email.VerificationTemplate
	if template == "" {
		template = "您的验证码是：[%s]，有效期为%d分钟，请勿泄露给他人。"
	}

	sendMsg := &ValidateCodeSend{
		NotifyTarget: "custom",
		SendSmsPure: struct {
			Content   string   `json:"content"`
			Phones    []string `json:"phones"`
			TplID     string   `json:"tpl_id"`
			TplParams []string `json:"tpl_params"`
		}{
			Phones:    []string{phone},
			Content:   fmt.Sprintf(template, verificationCode, conf.Sms.VerificationCodeValidTime),
			TplID:     tid,
			TplParams: tParamSet,
		},
		TplType: "custom",
	}
	if err := sendMessage(sendMsg); err != nil {
		logs.Warnf("failed to sendMessage error %s", err)
		return errors.ErrorSendValidateCode
	}
	logs.Infof("send message success ")

	return nil
}

// SendEmailVerificationCode 发送邮箱验证码
// 参数:
//   - email: 收件人邮箱地址
//   - verificationCode: 验证码
//
// 返回值:
//   - error: 发送失败时返回错误信息
func SendEmailVerificationCode(email, verificationCode string) error {
	data := conf.EmailSubjectConf.Zh

	subject := data.Subject
	template := conf.Email.VerificationTemplate
	if template == "" {
		template = "您的验证码是：[%s]，有效期为%d分钟，请勿泄露给他人。"
	}

	sendMsg := &ValidateCodeSend{
		NotifyTarget: "custom",
		SendEmailPure: struct {
			Content string   `json:"content"`
			Emails  []string `json:"emails"`
			Subject string   `json:"subject"`
		}{
			Content: fmt.Sprintf(template, verificationCode, conf.Sms.VerificationCodeValidTime),
			Subject: subject,
			Emails:  []string{email},
		},
		TplType: "custom",
	}

	if err := sendMessage(sendMsg); err != nil {
		logs.Warnf("failed to sendMessage error %s", err)
		return errors.ErrorSendValidateCode
	}

	logs.Infof("send message success ")

	return nil
}

func sendMessage(msg *ValidateCodeSend) error {
	byteData, err := json.Marshal(msg)
	if err != nil {
		logs.Warnf("failed to marshal error %s", err)
		return err
	}

	logs.Infof("sendMessage is %s", string(byteData))

	request, err := http.NewRequest(http.MethodPost, fmt.Sprintf("%s/sase_notify/api/v1/real_time_send_notify", conf.ThirdParty.LogTrackerAddress), bytes.NewBuffer(byteData))
	if err != nil {
		logs.Errorf("failed to make Request Data error %s", err)
		return err
	}
	request.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	resp, err := client.Do(request)
	if err != nil {
		logs.Warnf("请求发送错误:%s", err)
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return errors.ErrSmsSend
	}
	body, err := io.ReadAll(resp.Body)

	logs.Infof("sendMessage response is %s", string(body))

	return nil
}
