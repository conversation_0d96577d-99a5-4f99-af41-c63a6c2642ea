package common

import (
	"encoding/json"
	"fmt"
	"net/http"
	"ztna_server_auth/common/errors"

	"github.com/gin-gonic/gin"
)

type response struct {
	Error   int         `json:"error"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

// ResponseResult 响应结果
func ResponseResult(ctx *gin.Context, v interface{}, data ...interface{}) {
	response := newResponse(v)

	if len(data) > 0 {
		response.Data = data[0]
	}
	ctx.Header("Content-Type", "application/json")
	ctx.IndentedJSON(http.StatusOK, response)
}

func newResponse(v interface{}) *response {
	var resp *response

	switch v := v.(type) {
	case errors.ErrorCode:
		resp = &response{Error: v.Code, Message: v.Message, Data: ""}
	case error:
		resp = &response{Error: -1, Message: v.Error(), Data: ""}
	default:
		resp = &response{Error: 0, Message: "success", Data: v}
	}

	return resp
}

func (r *response) Encode() []byte {
	encoded, _ := json.Marshal(r)
	return encoded
}

func (r *response) Decode(v interface{}) error {
	switch r.Data.(type) {
	case string:
		return json.Unmarshal([]byte(r.Data.(string)), v)
	case []byte:
		return json.Unmarshal(r.Data.([]byte), v)
	default:
		return fmt.Errorf("cannot decode data of type: %T", r.Data)
	}
}
