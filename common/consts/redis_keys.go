package consts

import "fmt"

func GetSendMessageCodeRedisKey(key, phone string) string {
	return fmt.Sprintf("ztna_server_auth:%s:sendmessage:verification_code:%s", key, phone)
}

func GetSendMessageCodeRedisKeyForIp(key, ip string) string {
	return fmt.Sprintf("ztna_server_auth:%s:sendmessage:ip:%s", key, ip)
}

func GetValidatedTimeKey(key, cacheKey string) string {
	return fmt.Sprintf("ztna_server_auth:%s:validatedtime:%s", key, cacheKey)
}
func GetTimeDayForPhoneKey(key, cacheKey string) string {
	return fmt.Sprintf("ztna_server_auth:%s:time_day_for_phone:%s", key, cacheKey)
}

var (
	IPLoginFailedPrefix      = "auth:login_failed:ip:"
	AccountLoginFailedPrefix = "auth:login_failed:account:"
	IPBanPrefix              = "auth:ban:ip:"
	AccountBanPrefix         = "auth:ban:account:"
)
