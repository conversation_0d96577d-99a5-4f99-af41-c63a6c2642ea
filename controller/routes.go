// Package controller  define router
package controller

import (
	"ztna_server_auth/common/logs"
	"ztna_server_auth/controller/auth"
	"ztna_server_auth/controller/middleware"

	"github.com/gin-gonic/gin"
)

func RegisterRoutes(r *gin.Engine) {
	logs.PInfof("RegisterRoutes: start")
	r.Use(gin.Recovery())
	r.Use(auth.RequestLogger())
	r.Use(auth.CORS())
	// 创建认证中间件
	authMiddleware := auth.NewAuthMiddleware()
	baseGroup := r.Group("/sase/auth/api")

	v2 := baseGroup.Group("/v2")
	{
		authController := auth.NewAuthController()
		v2.POST("/captcha", authController.HandleSendCaptcha)                           // 发送验证码
		v2.POST("/refresh", authController.HandleRefreshTokenV2)                        // 刷新令牌端点
		v2.POST("/enterprise/get_login_type", authController.HandleEnterpriseLoginType) // 企业的登陆方式的配置

		v2.GET("/redirect_url", authController.HandleRedirectURL)                                                       // 移动到ztna-client中                                                  // 处理回调的地址
		v2.POST("/login/verification-code", authController.HandleVerificationCodeLoginV2, middleware.LoginMiddleware()) // 通用验证码登录 调接口的时候直接删除

		v2.POST("/verification-code/login", authController.HandleVerificationCodeLoginV2, middleware.LoginMiddleware())         // 验证码登录
		v2.POST("/third-party/ldap_account/login", authController.HandleLdapDomainAccountLoginV2, middleware.LoginMiddleware()) //ldap登录

		loginV2 := v2.Group("", authMiddleware.AuthenticateV2()) // 登录状态下的接口
		{
			loginV2.GET("/check_token", authController.HandleCheckToken, middleware.CheckUserLoginState()) // 验证token·
			loginV2.GET("/check_user_agreement", authController.HandleCheckUserAgreement)                  // 新版后续删除
			loginV2.GET("/validate", authController.ValidateTokenEndpoint)
			loginV2.DELETE("/clear_token", authController.HandlerClearToken)
		}

	}

	logs.PInfof("RegisterRoutes: end")
}
