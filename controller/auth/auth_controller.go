package auth

import (
	"context"
	"fmt"
	"net/http"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"

	"ztna_server_auth/common"
	"ztna_server_auth/common/config"
	"ztna_server_auth/common/consts"
	"ztna_server_auth/common/errors"
	"ztna_server_auth/common/logs"
	"ztna_server_auth/common/utils"
	"ztna_server_auth/dto"
	"ztna_server_auth/infrastructure/client"
	"ztna_server_auth/service"
)

var conf = config.Config()

// Controller  处理认证相关的HTTP请求
type Controller struct{}

// NewAuthController 创建新的认证控制器
func NewAuthController() *Controller {
	return &Controller{}
}

// HandleRefreshToken 处理刷新令牌请求
func (c *Controller) HandleRefreshTokenV2(ctx *gin.Context) {
	var request dto.RefreshTokenRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		common.ResponseResult(ctx, errors.InvalidParameter)
		return
	}

	// 刷新令牌
	tokenResponse, err := service.AuthService.RefreshTokenV2(ctx.Request.Context(), request.RefreshToken, ctx.GetHeader(consts.ClientID))
	if err != nil {
		common.ResponseResult(ctx, errors.InvalidRefreshToken)
		return
	}

	// 返回新令牌
	ctx.JSON(http.StatusOK, tokenResponse)
}

// ValidateTokenEndpoint 处理token验证请求
func (c *Controller) ValidateTokenEndpoint(ctx *gin.Context) {
	// 从请求头获取token
	userInfo, err := GetUserInfo(ctx)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, err)
		return
	}

	// 返回用户信息
	ctx.JSON(http.StatusOK, dto.TokenValidationResponse{
		Status: "success",
		User:   userInfo,
	})
}

func (c *Controller) HandleSendCaptcha(ctx *gin.Context) {
	var req dto.CaptchaRequest
	var err error

	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.ResponseResult(ctx, errors.InvalidParameter)
		return
	}

	// 确定发送类型：手机号或邮箱
	isPhoneMode := req.Phone != ""
	isEmailMode := req.Email != ""

	// 不能同时提供手机号和邮箱
	if (isPhoneMode && isEmailMode) || (!isEmailMode && !isPhoneMode) {
		common.ResponseResult(ctx, errors.InvalidParameter)
		return
	}

	if !conf.Service.IsTestMode {
		// 验证图形验证码
		if err := utils.VerifyImageCode(ctx, req.Ticket, req.RandStr); err != nil {
			common.ResponseResult(ctx, errors.ImageCaptchaError)
			return
		}
	}

	// 根据发送类型生成不同的 Redis key
	var redisKey, redisKeyValidTime, redisTimesDayForTargetKey string

	if isPhoneMode {
		redisKey = consts.GetSendMessageCodeRedisKey(req.Source, req.Phone)
		redisKeyValidTime = consts.GetValidatedTimeKey(req.Source, redisKey)
		redisTimesDayForTargetKey = consts.GetTimeDayForPhoneKey(req.Source, redisKey)
	} else {
		redisKey = consts.GetSendMessageCodeRedisKey(req.Source, req.Email)
		redisKeyValidTime = consts.GetValidatedTimeKey(req.Source, redisKey)
		redisTimesDayForTargetKey = consts.GetTimeDayForPhoneKey(req.Source, redisKey) // 复用手机号的key结构
	}

	redisTimesDayForIPKey := consts.GetSendMessageCodeRedisKeyForIp(req.Source, strings.ReplaceAll(utils.GetClientIp(ctx), ".", "_"))

	redisTimesDayForTarget, _ := client.Redis.Get(ctx, redisTimesDayForTargetKey).Result()
	redisTimesDayForTargetNum, _ := strconv.ParseInt(redisTimesDayForTarget, 10, 64)

	redisTimesDayForIP, _ := client.Redis.Get(ctx, redisTimesDayForIPKey).Result()
	redisTimesDayForIPNum, _ := strconv.ParseInt(redisTimesDayForIP, 10, 64)

	if redisTimesDayForTargetNum >= int64(conf.Sms.VerificationCodeTimesDayForPhone) {
		common.ResponseResult(ctx, errors.ErrSmsTimeDay)
		return
	}

	if redisTimesDayForIPNum >= int64(conf.Sms.VerificationCodeTimesDayForIp) {
		common.ResponseResult(ctx, errors.ErrSmsTimeDayForIp)
		return
	}

	var captcha string
	if conf.Service.IsTestMode {
		captcha = "123456" // 测试模式下，验证码为123456
	} else {
		captcha, err = utils.CreateCaptcha(6)
		if err != nil {
			common.ResponseResult(ctx, errors.ErrVerificationCodeCreate)
			return
		}
	}

	// 验证手机号或邮箱格式
	if isPhoneMode {
		regBool, err := regexp.MatchString(consts.Regs["phone"], req.Phone)
		if err != nil {
			common.ResponseResult(ctx, err)
			return
		}
		if !regBool {
			common.ResponseResult(ctx, errors.ErrVerificationCodePhone)
			return
		}
	} else if isEmailMode {
		regBool, err := regexp.MatchString(consts.Regs["email"], req.Email)
		if err != nil {
			common.ResponseResult(ctx, err)
			return
		}
		if !regBool {
			common.ResponseResult(ctx, errors.ErrVerificationCodeEmail)
			return
		}
	}

	redisVal, _ := client.Redis.Get(ctx, redisKey).Result()

	if redisVal != "" {
		common.ResponseResult(ctx, errors.ErrSendVerificationCode)
		return
	}

	targetNum, err := client.Redis.Incr(ctx, redisTimesDayForTargetKey).Result()
	logs.Infof("set redis times target key: %s val:%v error:%v", redisTimesDayForTargetKey, targetNum, err)

	ipNum, err := client.Redis.Incr(ctx, redisTimesDayForIPKey).Result()
	logs.Infof("set redis times ip key: %s val:%v error:%v", redisTimesDayForIPKey, ipNum, err)

	if targetNum == 1 {
		ok, err := client.Redis.ExpireAt(ctx, redisTimesDayForTargetKey, utils.GetNowForZeroTime(1)).Result()
		logs.Infof("set redis expire time target key: %s res:%v error:%v", redisTimesDayForTargetKey, ok, err)
	}

	if ipNum == 1 {
		ok, err := client.Redis.ExpireAt(ctx, redisTimesDayForIPKey, utils.GetNowForZeroTime(1)).Result()
		logs.Infof("set redis expire time ip key: %s res:%v error:%v", redisTimesDayForIPKey, ok, err)
	}

	_, err = client.Redis.Set(ctx, redisKey, captcha, time.Duration(conf.Sms.VerificationCodeInterval)*time.Second).Result()
	if err != nil {
		logs.Errorf("verification code set key1: %s error:%v", redisKey, err)
		common.ResponseResult(ctx, errors.ErrInternalServer)
		return
	}

	_, err = client.Redis.Set(ctx, redisKeyValidTime, captcha, time.Duration(conf.Sms.VerificationCodeValidTime)*time.Minute).Result()
	if err != nil {
		logs.Errorf("verification code set key2: %s error:%v", redisKeyValidTime, err)
		common.ResponseResult(ctx, errors.ErrInternalServer)
		return
	}

	// 根据类型发送验证码
	if isPhoneMode {
		// 发送短信验证码
		err = utils.SendMessage(req.Phone, consts.SmsTempVerificationCode, captcha)
		if err != nil {
			logs.Errorf("sms send code: %s:%s error:%v", req.Phone, captcha, err)
			common.ResponseResult(ctx, errors.ErrSmsSend)
			return
		}
		logs.Infof("短信验证码发送成功: %s", req.Phone)
	} else if isEmailMode {
		// 发送邮箱验证码
		err = utils.SendEmailVerificationCode(req.Email, captcha)
		if err != nil {
			logs.Errorf("email send code: %s:%s error:%v", req.Email, captcha, err)
			common.ResponseResult(ctx, errors.ErrSmsSend) // 复用短信发送错误
			return
		}
		logs.Infof("邮箱验证码发送成功: %s", req.Email)
	}

	common.ResponseResult(ctx, struct{}{})
}

func (c *Controller) HandleCheckToken(ctx *gin.Context) {

	expireTime, err := service.AuthService.GetTokenExpireInfo(ctx, &dto.BindHostInfoDataRequest{
		Token: GetAccessTokenFromCtx(ctx),
	})
	if err != nil {
		common.ResponseResult(ctx, err)
		return
	}

	common.ResponseResult(ctx, expireTime)
}

// 登录失败处理
func (c *Controller) handleLoginFail(ctx *gin.Context, req *dto.LoginAccountIP) {
	entSet, err := service.EnterpriseSettingService.FindEnterpriseSetting(ctx, req.ProductID, "account_security")
	if err != nil || entSet == nil {
		logs.Errorf("根据产品id: %s 查询企业设置失败: %v", req.ProductID, err)
		return
	}
	setting := entSet.GetAccountSetting()
	// IP防护
	if setting.BruteForceProtection.IPLoginSwitch {
		ipKey := fmt.Sprintf("%s%s:%s", consts.IPLoginFailedPrefix, req.ProductID, utils.GetClientIp(ctx))
		count, _ := client.Redis.Incr(ctx, ipKey).Result()
		client.Redis.Expire(ctx, ipKey, time.Duration(setting.BruteForceProtection.IPLoginConfig.Duration)*time.Minute)
		if int(count) >= setting.BruteForceProtection.IPLoginConfig.FailedNum {
			banKey := fmt.Sprintf("%s%s:%s", consts.IPBanPrefix, req.ProductID, utils.GetClientIp(ctx))
			client.Redis.Set(ctx, banKey, 1, time.Duration(setting.BruteForceProtection.IPLoginConfig.ProhibitIPDuration)*time.Minute)
		}
	}
	// 账号防护
	if setting.BruteForceProtection.AccountLoginSwitch {
		accKey := fmt.Sprintf("%s%s:%s", consts.AccountLoginFailedPrefix, req.ProductID, req.Account)
		count, _ := client.Redis.Incr(ctx, accKey).Result()
		client.Redis.Expire(ctx, accKey, time.Duration(setting.BruteForceProtection.AccountLoginConfig.Duration)*time.Minute)
		if int(count) >= setting.BruteForceProtection.AccountLoginConfig.FailedNum {
			banKey := fmt.Sprintf("%s%s:%s", consts.AccountBanPrefix, req.ProductID, req.Account)
			client.Redis.Set(ctx, banKey, 1, time.Duration(setting.BruteForceProtection.AccountLoginConfig.ProhibitLoginDuration)*time.Minute)
		}
	}
}

// 登录成功清理
func clearLoginFail(ctx *gin.Context, req *dto.LoginAccountIP) {
	client.Redis.Del(ctx, fmt.Sprintf("%s%s:%s", consts.IPLoginFailedPrefix, req.ProductID, utils.GetClientIp(ctx)))
	client.Redis.Del(ctx, fmt.Sprintf("%s%s:%s", consts.AccountLoginFailedPrefix, req.ProductID, req.Account))
}

// 检查IP是否被封禁
func isIPBanned(ctx context.Context, req *dto.LoginAccountIP, setting *dto.AccountSetting) bool {
	if !setting.BruteForceProtection.IPLoginSwitch {
		return false
	}
	key := fmt.Sprintf("%s%s:%s", consts.IPBanPrefix, req.ProductID, req.IP)
	res, err := client.Redis.Exists(ctx, key).Result()
	logs.Infof("get ipbanned key: %s res:%v error:%v", key, res, err)
	return res == 1
}

// 检查账号是否被封禁
func isAccountBanned(ctx context.Context, req *dto.LoginAccountIP, setting *dto.AccountSetting) bool {
	if !setting.BruteForceProtection.AccountLoginSwitch {
		return false
	}
	key := fmt.Sprintf("%s%s:%s", consts.AccountBanPrefix, req.ProductID, req.Account)

	res, err := client.Redis.Exists(ctx, key).Result()
	logs.Infof("get accountban key: %s res:%v error:%v", key, res, err)
	return res == 1
}

func (c *Controller) IPAndAccountBanCheck(ctx *gin.Context, req *dto.LoginAccountIP) error {
	entSet, err := service.EnterpriseSettingService.FindEnterpriseSetting(ctx, req.ProductID, "account_security")
	if err != nil || entSet == nil {
		logs.Errorf("根据产品id: %s 查询企业设置失败: %v", req.ProductID, err)
		return nil
	}
	req.IP = utils.GetClientIp(ctx)
	if isAccountBanned(ctx, req, entSet.GetAccountSetting()) {
		logs.Infof("product_id: %s, account: %s, ip: %s,account is banned", req.ProductID, req.Account, req.IP)
		_ = service.AuthService.SetUserInfoStatusLocked(ctx, req)
		return errors.New(errors.AccountBannedError.Code, fmt.Sprintf(errors.AccountBannedError.MessageEn, entSet.GetAccountSetting().BruteForceProtection.AccountLoginConfig.ProhibitLoginDuration),
			fmt.Sprintf(errors.AccountBannedError.Message, entSet.GetAccountSetting().BruteForceProtection.AccountLoginConfig.ProhibitLoginDuration))
	}

	if isIPBanned(ctx, req, entSet.GetAccountSetting()) {
		logs.Infof("product_id: %s, account: %s, ip: %s,ip is banned", req.ProductID, req.Account, req.IP)
		_ = service.AuthService.SetUserInfoStatusLocked(ctx, req)

		return errors.New(errors.IpBannedError.Code, fmt.Sprintf(errors.IpBannedError.MessageEn, entSet.GetAccountSetting().BruteForceProtection.IPLoginConfig.ProhibitIPDuration),
			fmt.Sprintf(errors.IpBannedError.Message, entSet.GetAccountSetting().BruteForceProtection.IPLoginConfig.ProhibitIPDuration))
	}

	return nil
}

func (c *Controller) HandleLdapDomainAccountLoginV2(ctx *gin.Context) {
	req := &dto.LdapDomainAccountLoginRequest{
		ClientID: ctx.GetHeader(consts.ClientID),
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.ResponseResult(ctx, errors.InvalidParameter)
		return
	}

	req.Ticket = ctx.GetHeader(consts.RMTicket)

	tokenResponse, err := service.AuthService.LdapDomainAccountLoginV2(ctx, req)
	if err != nil {
		common.ResponseResult(ctx, err)
		return
	}

	tokenResponse.Ticket = ctx.GetHeader(consts.RMTicket)
	tokenResponse.LoginType = consts.LdapDomainAccount
	SetUserLoginInfoToContext(ctx, tokenResponse)
	common.ResponseResult(ctx, tokenResponse)
}

//func (c *Controller) HandleSSOLogin(ctx *gin.Context) {
//	var req = dto.SSORequest{ClientID: ctx.GetHeader(consts.ClientID)}
//
//	if err := ctx.ShouldBindJSON(&req); err != nil {
//		common.ResponseResult(ctx, errors.InvalidParameter)
//		return
//	}
//
//	tokenResponse, err := service.AuthService.SSOLogin(ctx, &req)
//	if err != nil {
//		common.ResponseResult(ctx, err)
//		return
//	}
//
//	tokenResponse.LoginType = consts.SSO
//	tokenResponse.Ticket = ctx.GetHeader(consts.RMTicket)
//	SetUserLoginInfoToContext(ctx, tokenResponse)
//
//	common.ResponseResult(ctx, tokenResponse)
//}

func (c *Controller) HandleEnterpriseLoginType(ctx *gin.Context) {

	var req = &dto.EnterpriseLoginTypeRequest{}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		common.ResponseResult(ctx, errors.InvalidParameter)
		return
	}

	resp, err := service.EnterpriseSettingService.FindEnterpriseLoginType(ctx, req)
	if err != nil {
		logs.Errorf("failed to call FindEnterpriseSetting error: %s", err)
		common.ResponseResult(ctx, []*dto.EnterpriseLoginSetting{})
		return
	}
	common.ResponseResult(ctx, resp)
}

func (c *Controller) HandleCheckUserAgreement(ctx *gin.Context) {
	// 从请求头获取token
	userInfo, err := GetUserInfo(ctx)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, err)
		return
	}

	err = service.UserAgreementService.CheckAgreement(ctx, userInfo.ProductID, userInfo.UserCode, "", ctx.GetHeader(consts.ClientID))
	if err != nil {
		logs.Warnf("failed to call CheckAgreement error: %s", err)
	}

	common.ResponseResult(ctx, struct{}{})

}

// HandleVerificationCodeLoginV2 处理通用验证码登录（支持手机号和邮箱）
func (c *Controller) HandleVerificationCodeLoginV2(ctx *gin.Context) {
	var request dto.VerificationCodeLoginRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		common.ResponseResult(ctx, errors.InvalidParameter)
		return
	}

	// 验证参数：手机号和邮箱不能同时为空，也不能同时有值
	if request.Phone == "" && request.Email == "" {
		common.ResponseResult(ctx, errors.InvalidParameter)
		return
	}

	// 确定登录类型和账户标识
	var account, loginType string
	var redisKey string

	if request.Phone != "" {
		account = request.Phone
		loginType = consts.PhoneLogin
		redisKey = consts.GetSendMessageCodeRedisKey(string(dto.Login), request.Phone)
	} else {
		account = request.Email
		loginType = consts.EmailLogin
		redisKey = consts.GetSendMessageCodeRedisKey(string(dto.Login), request.Email)
	}

	// IP和账户封禁检查
	if err := c.IPAndAccountBanCheck(ctx, &dto.LoginAccountIP{
		Account:   account,
		ProductID: request.ProductID,
		OrgCode:   request.OrgCode,
	}); err != nil {
		common.ResponseResult(ctx, err)
		return
	}

	// 验证验证码
	cacheCode, err := client.Redis.Get(ctx, redisKey).Result()
	if err != nil {
		c.handleLoginFail(ctx, &dto.LoginAccountIP{
			Account:   account,
			ProductID: request.ProductID,
		})
		common.ResponseResult(ctx, errors.ErrorOfCaptcha)
		return
	}

	if request.Code != cacheCode {
		c.handleLoginFail(ctx, &dto.LoginAccountIP{
			Account:   account,
			ProductID: request.ProductID,
		})
		common.ResponseResult(ctx, errors.ErrorOfCaptcha)
		return
	}

	// 生成token
	tokenResponse, err := service.AuthService.VerificationCodeLoginV2(ctx, &dto.VerificationCodeLoginRequest{
		OrgCode:   request.OrgCode,
		ProductID: request.ProductID,
		Phone:     request.Phone,
		Email:     request.Email,
		Code:      request.Code,
		ClientID:  ctx.GetHeader(consts.ClientID),
		Ticket:    ctx.GetHeader(consts.RMTicket),
	})
	if err != nil {
		c.handleLoginFail(ctx, &dto.LoginAccountIP{
			Account:   account,
			ProductID: request.ProductID,
		})
		common.ResponseResult(ctx, err)
		return
	}

	clearLoginFail(ctx, &dto.LoginAccountIP{
		Account:   account,
		ProductID: request.ProductID,
	})

	tokenResponse.Ticket = ctx.GetHeader(consts.RMTicket)
	tokenResponse.LoginType = loginType
	SetUserLoginInfoToContext(ctx, tokenResponse)

	common.ResponseResult(ctx, tokenResponse)
}

func (c *Controller) HandleRedirectURL(ctx *gin.Context) {

	common.ResponseResult(ctx, map[string]any{

		"redirect_url": conf.ThirdParty.ConsoleURL,
	})
}

func (c *Controller) HandlerClearToken(ctx *gin.Context) {
	// 从请求头获取token
	userInfo, err := GetUserInfo(ctx)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, err)
		return
	}

	err = service.AuthService.ClearToken(ctx, &dto.UserTokenData{
		UserID:    userInfo.UserCode,
		ProductID: userInfo.ProductID,
		ClientID:  ctx.GetHeader(consts.ClientID),
		Token:     ctx.GetString("access_token"),
	})
	if err != nil {
		logs.Errorf("failed to ClearToken Data Value error %s", err)
		common.ResponseResult(ctx, errors.ErrorUserClearToken)
		return
	}

	common.ResponseResult(ctx, err)
}
