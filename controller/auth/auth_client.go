// Package auth define auth middleware
package auth

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"ztna_server_auth/common/logs"
	"ztna_server_auth/dto"
)

// ClientConfig 认证客户端配置
type ClientConfig struct {
	AuthServerURL string        // 认证服务器地址
	Timeout       time.Duration // HTTP 客户端超时时间
}

// Client 认证客户端
type Client struct {
	config     ClientConfig
	httpClient *http.Client
}

// NewClient 创建新的认证客户端
func NewClient(config ClientConfig) *Client {
	if config.Timeout == 0 {
		config.Timeout = 5 * time.Second
	}

	return &Client{
		config: config,
		httpClient: &http.Client{
			Timeout: config.Timeout,
		},
	}
}

// ValidateToken 验证token并返回用户信息
func (c *Client) ValidateToken(ctx context.Context, token string) (*dto.AuthUserInfo, error) {
	req, err := http.NewRequestWithContext(ctx, "GET", c.config.AuthServerURL+"/api/auth/validate", nil)
	if err != nil {
		return nil, fmt.Errorf("create request failed: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+token)

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("request failed: %w", err)
	}
	defer func() {
		_ = resp.Body.Close()

	}()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unauthorized: status code %d", resp.StatusCode)
	}

	var validationResp dto.TokenValidationResponse
	if err := json.NewDecoder(resp.Body).Decode(&validationResp); err != nil {
		return nil, fmt.Errorf("decode response failed: %w", err)
	}

	return validationResp.User, nil
}

// AuthMiddleware 创建认证中间件
func (c *Client) AuthMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 从请求头获取token
		authHeader := r.Header.Get("Authorization")
		if authHeader == "" {
			http.Error(w, "No authorization header", http.StatusUnauthorized)
			return
		}

		// 解析Bearer token
		parts := strings.Split(authHeader, " ")
		if len(parts) != 2 || parts[0] != "Bearer" {
			http.Error(w, "Invalid authorization header format", http.StatusUnauthorized)
			return
		}

		token := parts[1]

		// 验证token
		user, err := c.ValidateToken(r.Context(), token)
		if err != nil {
			logs.PErrorf("Token validation failed: %v", err)
			http.Error(w, "Unauthorized", http.StatusUnauthorized)
			return
		}

		// 将用户信息添加到请求上下文
		ctx := context.WithValue(r.Context(), UserContextKey, user)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// UserContextKey 用于从上下文获取用户信息的key
type userContextKey struct{}

var UserContextKey = userContextKey{}
