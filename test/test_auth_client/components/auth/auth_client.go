package auth

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"test_auth_client/common/dto"
	"test_auth_client/common/xlogs"
)

// ClientConfig 认证客户端配置
type ClientConfig struct {
	AuthServerURL string        // 认证服务器地址
	Timeout       time.Duration // HTTP 客户端超时时间
}

// Client  认证客户端
type Client struct {
	config     ClientConfig
	httpClient *http.Client
}

// NewAuthClient 创建新的认证客户端
func NewAuthClient(config ClientConfig) *Client {
	if config.Timeout == 0 {
		config.Timeout = 5 * time.Second
	}

	return &Client{
		config: config,
		httpClient: &http.Client{
			Timeout: config.Timeout,
		},
	}
}

// ValidateToken 验证token并返回用户信息
func (c *Client) ValidateToken(ctx context.Context, token string) (*dto.UserInfo, error) {
	validateURL := c.config.AuthServerURL + "/api/auth/validate"
	xlogs.PInfof("Validating token at URL: %s", validateURL)

	req, err := http.NewRequestWithContext(ctx, "GET", validateURL, nil)
	if err != nil {
		xlogs.PErrorf("Failed to create request: %v", err)
		return nil, fmt.Errorf("create request failed: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+token)
	xlogs.PInfof("Request headers: %+v", req.Header)

	resp, err := c.httpClient.Do(req)
	if err != nil {
		xlogs.PErrorf("HTTP request failed: %v", err)
		return nil, fmt.Errorf("request failed: %w", err)
	}
	defer func() {
		_ = resp.Body.Close()
	}()

	xlogs.PInfof("Response status: %s", resp.Status)
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		xlogs.PErrorf("Unauthorized response: status=%d, body=%s", resp.StatusCode, string(body))
		return nil, fmt.Errorf("unauthorized: status code %d", resp.StatusCode)
	}

	var validationResp dto.TokenValidationResponse
	if err := json.NewDecoder(resp.Body).Decode(&validationResp); err != nil {
		xlogs.PErrorf("Failed to decode response: %v", err)
		return nil, fmt.Errorf("decode response failed: %w", err)
	}

	xlogs.PInfof("Token validation successful for user: %+v", validationResp.User)
	return validationResp.User, nil
}

// AuthMiddleware 创建认证中间件
func (c *Client) AuthMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 添加 CORS 头
		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")

		// 处理 OPTIONS 请求
		if r.Method == "OPTIONS" {
			w.WriteHeader(http.StatusOK)
			return
		}

		xlogs.PInfof("Processing request: %s %s", r.Method, r.URL.Path)
		xlogs.PInfof("Request headers: %+v", r.Header)

		// 从请求头获取token
		authHeader := r.Header.Get("Authorization")
		if authHeader == "" {
			xlogs.PErrorf("No authorization header found")
			http.Error(w, "No authorization header", http.StatusUnauthorized)
			return
		}

		// 解析Bearer token
		parts := strings.Split(authHeader, " ")
		if len(parts) != 2 || parts[0] != "Bearer" {
			xlogs.PErrorf("Invalid authorization header format: %s", authHeader)
			http.Error(w, "Invalid authorization header format", http.StatusUnauthorized)
			return
		}

		token := parts[1]
		xlogs.PInfof("Extracted token: %s", token[:10]+"...")

		// 验证token
		user, err := c.ValidateToken(r.Context(), token)
		if err != nil {
			xlogs.PErrorf("Token validation failed: %v", err)
			http.Error(w, "Unauthorized", http.StatusUnauthorized)
			return
		}

		// 将用户信息添加到请求上下文
		ctx := context.WithValue(r.Context(), UserContextKey, user)
		xlogs.PInfof("Added user to context: %+v", user)

		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// UserContextKey 用于从上下文获取用户信息的key
type userContextKey struct{}

var UserContextKey = userContextKey{}

// GetUserFromContext 从上下文中获取用户信息
func GetUserFromContext(ctx context.Context) (*dto.UserInfo, bool) {
	user, ok := ctx.Value(UserContextKey).(*dto.UserInfo)
	return user, ok
}
