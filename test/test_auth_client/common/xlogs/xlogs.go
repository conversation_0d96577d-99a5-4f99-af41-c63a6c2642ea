package xlogs

import (
	"context"
	"fmt"
	"io"
	"log/slog"
	"os"
	"strings"
	"time"

	rotatelogs "github.com/lestrrat-go/file-rotatelogs"
)

type XLogs struct {
	conf   *Config
	logger *slog.Logger
}

type Config struct {
	Stdout        bool
	Dir           string
	Type          string
	Level         string
	RotationTime  time.Duration
	RotationCount uint
}

func NameToLevel(name string) slog.Level {

	levelMappings := map[string]slog.Level{
		"fatal": slog.LevelError + 4,
		"error": slog.LevelError,
		"warn":  slog.LevelWarn,
		"info":  slog.LevelInfo,
		"debug": slog.LevelDebug,
		"trace": slog.LevelDebug - 4,
	}
	name = strings.ToLower(name)
	if level, ok := levelMappings[name]; ok {
		return level
	}
	return slog.LevelInfo
}

func DefaultConfig() *Config {
	return &Config{
		Stdout:        true,
		Dir:           "log",
		Type:          "text",
		Level:         "info",
		RotationTime:  time.Hour * 24,
		RotationCount: 3,
	}
}

func New(conf *Config) *XLogs {

	logfileName := fmt.Sprintf("%s/%%Y%%m%%d.log", conf.Dir)
	rotateLog, _ := rotatelogs.New(
		logfileName,
		rotatelogs.WithRotationTime(conf.RotationTime),
		rotatelogs.WithRotationCount(conf.RotationCount),
	)
	var logger *slog.Logger
	switch conf.Type {
	case "text":
		logger = slog.New(
			slog.NewTextHandler(
				io.MultiWriter(os.Stdout, rotateLog),
				&slog.HandlerOptions{
					Level: NameToLevel(conf.Level),
				},
			),
		)
	case "json":
		logger = slog.New(
			slog.NewJSONHandler(
				io.MultiWriter(os.Stdout, rotateLog),
				&slog.HandlerOptions{
					Level: NameToLevel(conf.Level),
				},
			),
		)
	default:
		logger = slog.New(
			slog.NewTextHandler(
				io.MultiWriter(os.Stdout, rotateLog),
				&slog.HandlerOptions{
					Level: NameToLevel(conf.Level),
				},
			),
		)
	}
	return &XLogs{logger: logger, conf: conf}
}

func (l *XLogs) WithContext(ctx context.Context, key string) *XLogs {
	return &XLogs{
		logger: l.logger.With(key, ctx.Value(key)),
	}
}

func (l *XLogs) Info(args ...interface{}) {
	l.logger.Info(fmt.Sprint(args...))
}

func (l *XLogs) PInfo(args ...interface{}) {
	if NameToLevel(l.conf.Level) <= NameToLevel("info") {
		fmt.Println("\033[5;32m", time.Now(), fmt.Sprint(args...), "\033[0m")
	}
}

func (l *XLogs) Infof(format string, args ...interface{}) {
	l.logger.Info(fmt.Sprintf(format, args...))
}

func (l *XLogs) PInfof(format string, args ...interface{}) {
	if NameToLevel(l.conf.Level) <= NameToLevel("info") {
		fmt.Println("\033[5;32m", time.Now(), fmt.Sprintf(format, args...), "\033[0m")
	}
}

func (l *XLogs) Warn(args ...interface{}) {
	l.logger.Warn(fmt.Sprint(args...))
}

func (l *XLogs) PWarn(args ...interface{}) {
	if NameToLevel(l.conf.Level) <= NameToLevel("warn") {
		fmt.Println("\033[5;33m", time.Now(), fmt.Sprint(args...), "\033[0m")
	}
}

func (l *XLogs) Warnf(format string, args ...interface{}) {
	l.logger.Warn(fmt.Sprintf(format, args...))
}

func (l *XLogs) PWarnf(format string, args ...interface{}) {
	if NameToLevel(l.conf.Level) <= NameToLevel("warn") {
		fmt.Println("\033[5;33m", time.Now(), fmt.Sprintf(format, args...), "\033[0m")
	}
}

func (l *XLogs) Error(args ...interface{}) {
	l.logger.Error(fmt.Sprint(args...))
}

func (l *XLogs) PError(args ...interface{}) {
	if NameToLevel(l.conf.Level) <= NameToLevel("error") {
		fmt.Println("\033[5;31m", time.Now(), fmt.Sprint(args...), "\033[0m")
	}
}

func (l *XLogs) Errorf(format string, args ...interface{}) {
	l.logger.Error(fmt.Sprintf(format, args...))
}

func (l *XLogs) PErrorf(format string, args ...interface{}) {
	if NameToLevel(l.conf.Level) <= NameToLevel("error") {
		fmt.Println("\033[5;31m", time.Now(), fmt.Sprintf(format, args...), "\033[0m")
	}
}

func (l *XLogs) Fatal(args ...interface{}) {
	l.logger.Log(context.Background(), slog.Level(12), fmt.Sprint(args...))
}

func (l *XLogs) PFatal(args ...interface{}) {
	if NameToLevel(l.conf.Level) <= NameToLevel("fatal") {
		fmt.Println("\033[5;34m", time.Now(), fmt.Sprint(args...), "\033[0m")
	}
}

func (l *XLogs) Fatalf(format string, args ...interface{}) {
	l.logger.Log(context.Background(), slog.Level(12), fmt.Sprintf(format, args...))
}

func (l *XLogs) PFatalf(format string, args ...interface{}) {
	if NameToLevel(l.conf.Level) <= NameToLevel("fatal") {
		fmt.Println("\033[5;34m", time.Now(), fmt.Sprintf(format, args...), "\033[0m")
	}
}

func (l *XLogs) Trace(args ...interface{}) {
	l.logger.Log(context.Background(), slog.Level(-8), fmt.Sprint(args...))
}

func (l *XLogs) PTrace(args ...interface{}) {
	if NameToLevel(l.conf.Level) <= NameToLevel("trace") {
		fmt.Println("\033[5;35m", time.Now(), fmt.Sprint(args...), "\033[0m")
	}
}

func (l *XLogs) Tracef(format string, args ...interface{}) {
	l.logger.Log(context.Background(), slog.Level(-8), fmt.Sprintf(format, args...))
}

func (l *XLogs) PTracef(format string, args ...interface{}) {
	if NameToLevel(l.conf.Level) <= NameToLevel("trace") {
		fmt.Println("\033[5;35m", time.Now(), fmt.Sprintf(format, args...), "\033[0m")
	}
}

func (l *XLogs) Debug(args ...interface{}) {
	l.logger.Debug(fmt.Sprint(args...))
}

func (l *XLogs) PDebug(args ...interface{}) {
	if NameToLevel(l.conf.Level) <= NameToLevel("debug") {
		fmt.Println("\033[5;36m", time.Now(), fmt.Sprint(args...), "\033[0m")
	}
}

func (l *XLogs) Debugf(format string, args ...interface{}) {
	l.logger.Debug(fmt.Sprintf(format, args...))
}

func (l *XLogs) PDebugf(format string, args ...interface{}) {
	if NameToLevel(l.conf.Level) <= NameToLevel("debug") {
		fmt.Println("\033[5;36m", time.Now(), fmt.Sprintf(format, args...), "\033[0m")
	}
}
