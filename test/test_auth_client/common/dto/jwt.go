package dto

import (
	"time"

	"github.com/golang-jwt/jwt/v5"
)

// JWTClaims 定义JWT载荷结构
type JWTClaims struct {
	UserId    string   `json:"user_id"`
	Username  string   `json:"username,omitempty"`
	Email     string   `json:"email,omitempty"`
	Roles     []string `json:"roles,omitempty"`
	TokenType string   `json:"token_type"` // "access" 或 "refresh"
	jwt.RegisteredClaims
}

// JWTConfig JWT配置
type JWTConfig struct {
	SecretKey     string        `mapstructure:"secret_key"`
	AccessExpiry  time.Duration `mapstructure:"access_expiry"`
	RefreshExpiry time.Duration `mapstructure:"refresh_expiry"`
	Issuer        string        `mapstructure:"issuer"`
}
