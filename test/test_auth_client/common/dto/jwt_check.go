package dto

type JwtCheckRequest struct {
	ClientId string `json:"client_id"`
}

type JwtCheckResponse struct {
	OrgName  string `json:"org_name"`
	ClientId string `json:"client_id"`
	UserId   string `json:"uid"`
	UserName string `json:"user_name"`
	Channel  string `json:"channel"`
}

type JwtBaseReceiveResponse struct {
	Error   int         `json:"error"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}
