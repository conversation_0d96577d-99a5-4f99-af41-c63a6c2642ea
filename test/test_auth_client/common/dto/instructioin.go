package dto

type InstructionsRecord struct {
	Id              string                   `json:"seq_no" bson:"_id"`
	InstructionName string                   `json:"instruction_name" bson:"instruction_name"`
	Params          map[string]interface{}   `json:"params" bson:"-"`
	BatchParams     []map[string]interface{} `json:"batch_params" bson:"-"`
	CreateTime      int64                    `json:"create_time" bson:"create_time"`
	HostName        string                   `json:"host_name" bson:"host_name"`
	InstructionType int                      `json:"instruction_type" bson:"instruction_type"` // 0人工响应、 1自动响应
	UserName        string                   `json:"user_name" bson:"-"`
}

type InstructionPullTaskMessageRequest struct {
	OrgName  string `json:"org_name"`
	ClientId string `json:"client_id"`
	Limit    int    `json:"limit"`
}

type InstructionPullTaskMessageResponse struct {
	Error   int                   `json:"error"`
	Message string                `json:"message"`
	Data    []*InstructionsRecord `json:"data"`
}

type InstructionUpdateTaskStatusRequest struct {
	OrgName  string   `json:"org_name"`
	ClientId string   `json:"client_id"`
	TaskId   []string `json:"task_id"`
}

type InstructionUpdateTaskStatusResponse struct {
	Error   int         `json:"error"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

type MessageInstructionsRecord struct {
	Data interface{} `json:"data"`
}

type InstructionKafkaWriteStruct struct {
	Data     string `json:"data"`
	ClientId string `json:"client_id"`
	OrgName  string `json:"org_name"`
}

type KafkaWriteStruct struct {
	ConnectId string `json:"connect_id"`
	ClientId  string `json:"client_id"`
	OrgName   string `json:"org_name"`
}
