package dto

import (
	"encoding/json"
	"time"
)

// UserInfo 用户信息
type UserInfo struct {
	UserId       string    `json:"user_id"`
	Username     string    `json:"username"`
	Email        string    `json:"email,omitempty"`
	Phone        string    `json:"phone,omitempty"`
	OrgName      string    `json:"org_name,omitempty"`
	PasswordHash string    `json:"password_hash,omitempty"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
	// 第三方登录相关信息
	ThirdPartyInfo map[string]interface{} `json:"third_party_info,omitempty"`
}

// TokenResponse OAuth2.0 令牌响应
type TokenResponse struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	TokenType    string `json:"token_type"`
	ExpiresIn    int    `json:"expires_in"`
	UserId       string `json:"user_id"`
	Username     string `json:"username"`
}

// OAuthAuthorizeRequest OAuth2.0 授权请求
type OAuthAuthorizeRequest struct {
	ResponseType string `json:"response_type"`
	ClientId     string `json:"client_id"`
	RedirectUri  string `json:"redirect_uri"`
	Scope        string `json:"scope"`
	State        string `json:"state"`
}

// OAuthTokenRequest OAuth2.0 令牌请求
type OAuthTokenRequest struct {
	GrantType    string `json:"grant_type"`
	Code         string `json:"code,omitempty"`
	RedirectUri  string `json:"redirect_uri,omitempty"`
	ClientId     string `json:"client_id"`
	ClientSecret string `json:"client_secret"`
	RefreshToken string `json:"refresh_token,omitempty"`
	Username     string `json:"username,omitempty"`
	Password     string `json:"password,omitempty"`
}

// LoginRequest 登录请求
type LoginRequest struct {
	Username string `json:"username"`
	Password string `json:"password"`
}

// ThirdPartyLoginRequest 第三方登录请求
type ThirdPartyLoginRequest struct {
	Provider string `json:"provider"`
	Code     string `json:"code"`
}

// RefreshTokenRequest 刷新令牌请求
type RefreshTokenRequest struct {
	RefreshToken string `json:"refresh_token"`
}

// TokenValidationResponse 定义token验证响应
type TokenValidationResponse struct {
	Status string    `json:"status"`
	User   *UserInfo `json:"user"`
}

// UnmarshalUserInfo 反序列化用户信息
func UnmarshalUserInfo(data string) (*UserInfo, error) {
	user := &UserInfo{}
	err := json.Unmarshal([]byte(data), user)
	return user, err
}
