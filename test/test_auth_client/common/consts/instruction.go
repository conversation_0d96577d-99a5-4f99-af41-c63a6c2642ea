package consts

var InstructionNameMapClientRouter = map[string]string{
	"kill_ps":                 "/api/v1/kill_ps",
	"batch_kill_ps":           "/api/v1/batch_kill_ps",
	"get_suspicious_file":     "/api/v1/get_suspicious_file",
	"batch_quarantine_file":   "/api/v1/batch_quarantine_file",
	"delete_quarantine_file":  "/api/v1/delete_quarantine_file",
	"recover_quarantine_file": "/api/v1/recover_quarantine_file",
	"process_dump":            "/api/v1/process_dump",
	"image_analyze":           "/api/v1/image_analyze",
	"process_analyze":         "/api/v1/process_analyze",
	"recover_network":         "/api/v1/recover_network",
	"quarantine_network":      "/api/v1/quarantine_network",
	"list_ps":                 "/api/v1/list_ps",
	"quarantine_file":         "/api/v1/quarantine_file",
	"stop_scan_virus":         "/api/v1/stop_scan_virus",
	"scan_virus":              "/api/v1/scan_virus",
}

const (
	InstructionGetTaskUrl          = "/r3/instructions_internal/v1/pull_task_message"
	InstructionUpdateTaskStatusUrl = "/r3/instructions_internal/v1/update_task_status"
)
