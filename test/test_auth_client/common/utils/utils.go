package utils

import (
	"fmt"
	"strconv"
	"time"
)

func ParseTimeString(str string) (int64, error) {
	if str == "" {
		return 0, fmt.Errorf("time string is empty")
	}

	var ts int64 = 0
	numbers := make([]byte, 0)

	for i := 0; i < len(str); i++ {
		switch str[i] {
		case '0', '1', '2', '3', '4', '5', '6', '7', '8', '9':
			numbers = append(numbers, str[i])
		case 'd':
			value, _ := strconv.ParseInt(string(numbers), 10, 32)
			ts += int64(time.Hour*24) * value
			numbers = make([]byte, 0)
		case 'h':
			value, _ := strconv.ParseInt(string(numbers), 10, 32)
			ts += int64(time.Hour) * value
			numbers = make([]byte, 0)
		case 'm':
			value, _ := strconv.ParseInt(string(numbers), 10, 32)
			ts += int64(time.Minute) * value
			numbers = make([]byte, 0)
		case 's':
			value, _ := strconv.ParseInt(string(numbers), 10, 32)
			ts += int64(time.Second) * value
			numbers = make([]byte, 0)
		default:
			return 0, fmt.Errorf("invalid char `%c` found in time string", str[i])
		}
	}

	return ts / int64(time.Second), nil
}
