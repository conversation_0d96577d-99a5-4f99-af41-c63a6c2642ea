package utils

import (
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"time"

	"golang.org/x/crypto/bcrypt"
)

// HashPassword 对密码进行哈希处理
func HashPassword(password string) (string, error) {
	bytes, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	return string(bytes), err
}

// VerifyPassword 验证密码
func VerifyPassword(password, hash string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
	return err == nil
}

// GenerateRandomToken 生成随机令牌
func GenerateRandomToken() string {
	// 生成32字节的随机数
	b := make([]byte, 32)
	_, err := rand.Read(b)
	if err != nil {
		// 如果读取随机数失败，则使用时间戳作为备用
		timestamp := fmt.Sprintf("%d", time.Now().UnixNano())
		hash := sha256.Sum256([]byte(timestamp))
		return hex.EncodeToString(hash[:])
	}

	return base64.URLEncoding.EncodeToString(b)
}

// JsonMarshal JSON序列化
func JsonMarshal(v interface{}) (string, error) {
	bytes, err := json.Marshal(v)
	if err != nil {
		return "", err
	}
	return string(bytes), nil
}

// JsonUnmarshal JSON反序列化
func JsonUnmarshal(data string, v interface{}) error {
	return json.Unmarshal([]byte(data), v)
}
