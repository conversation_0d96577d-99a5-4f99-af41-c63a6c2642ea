package main

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"time"

	"test_auth_client/common/xlogs"
	"test_auth_client/components/auth"
)

// Config 服务配置结构
type Config struct {
	Server struct {
		Port         string `json:"port"`
		ReadTimeout  string `json:"read_timeout"`
		WriteTimeout string `json:"write_timeout"`
		IdleTimeout  string `json:"idle_timeout"`
	} `json:"server"`
	Auth struct {
		ServerURL string `json:"server_url"`
		Timeout   string `json:"timeout"`
	} `json:"auth"`
}

// ServerConfig 转换后的服务器配置
type ServerConfig struct {
	Port         string
	ReadTimeout  time.Duration
	WriteTimeout time.Duration
	IdleTimeout  time.Duration
}

// AuthConfig 转换后的认证配置
type AuthConfig struct {
	ServerURL string
	Timeout   time.Duration
}

// LoadConfig 从文件加载配置
func LoadConfig(filename string) (*ServerConfig, *AuthConfig, error) {
	log.Printf("Loading config from file: %s", filename)
	file, err := os.Open(filename)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to open config file: %w", err)
	}
	defer func() {
		_ = file.Close()
	}()

	var config Config
	if err := json.NewDecoder(file).Decode(&config); err != nil {
		return nil, nil, fmt.Errorf("failed to decode config: %w", err)
	}

	// 解析服务器配置中的时间duration
	readTimeout, err := time.ParseDuration(config.Server.ReadTimeout)
	if err != nil {
		return nil, nil, fmt.Errorf("invalid read_timeout: %w", err)
	}
	writeTimeout, err := time.ParseDuration(config.Server.WriteTimeout)
	if err != nil {
		return nil, nil, fmt.Errorf("invalid write_timeout: %w", err)
	}
	idleTimeout, err := time.ParseDuration(config.Server.IdleTimeout)
	if err != nil {
		return nil, nil, fmt.Errorf("invalid idle_timeout: %w", err)
	}

	// 解析认证配置中的时间duration
	authTimeout, err := time.ParseDuration(config.Auth.Timeout)
	if err != nil {
		return nil, nil, fmt.Errorf("invalid auth timeout: %w", err)
	}

	serverConfig := &ServerConfig{
		Port:         config.Server.Port,
		ReadTimeout:  readTimeout,
		WriteTimeout: writeTimeout,
		IdleTimeout:  idleTimeout,
	}

	authConfig := &AuthConfig{
		ServerURL: config.Auth.ServerURL,
		Timeout:   authTimeout,
	}

	log.Printf("Loaded server config: %+v", serverConfig)
	log.Printf("Loaded auth config: %+v", authConfig)

	return serverConfig, authConfig, nil
}

// Response 通用响应结构
type Response struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// UserService 用户服务
type UserService struct {
	authClient *auth.Client
}

// NewUserService 创建用户服务实例
func NewUserService(authConfig *AuthConfig) *UserService {
	log.Printf("Creating new user service with auth server URL: %s", authConfig.ServerURL)
	// 创建认证客户端，连接到认证服务
	authClient := auth.NewAuthClient(auth.ClientConfig{
		AuthServerURL: authConfig.ServerURL,
		Timeout:       authConfig.Timeout,
	})

	return &UserService{
		authClient: authClient,
	}
}

// handleUserInfo 处理获取用户信息的请求
func (s *UserService) handleUserInfo(w http.ResponseWriter, r *http.Request) {
	log.Printf("Received request for user info. Method: %s, URL: %s", r.Method, r.URL.Path)
	log.Printf("Request headers: %+v", r.Header)

	// 从上下文中获取用户信息（由认证中间件注入）
	user, ok := auth.GetUserFromContext(r.Context())
	if !ok {
		log.Printf("Failed to get user info from context")
		writeJSON(w, http.StatusInternalServerError, Response{
			Code:    500,
			Message: "Failed to get user info from context",
		})
		return
	}

	log.Printf("Successfully retrieved user info: %+v", user)
	// 返回用户信息
	writeJSON(w, http.StatusOK, Response{
		Code:    200,
		Message: "Success",
		Data:    user,
	})
}

// handleHealthCheck 处理健康检查请求
func (s *UserService) handleHealthCheck(w http.ResponseWriter, r *http.Request) {
	log.Printf("Received health check request. Method: %s, URL: %s", r.Method, r.URL.Path)
	writeJSON(w, http.StatusOK, Response{
		Code:    200,
		Message: "Service is healthy",
	})
	log.Printf("Health check completed successfully")
}

// writeJSON 辅助函数，用于写入 JSON 响应
func writeJSON(w http.ResponseWriter, status int, v interface{}) {
	w.Header().Set("Content-Type", "application/json")
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")

	// 处理 OPTIONS 请求
	if r, ok := v.(*http.Request); ok && r.Method == "OPTIONS" {
		w.WriteHeader(http.StatusOK)
		return
	}

	w.WriteHeader(status)
	if err := json.NewEncoder(w).Encode(v); err != nil {
		log.Printf("Error encoding response: %v", err)
	} else {
		log.Printf("Response sent successfully: %+v", v)
	}
}

// loggingMiddleware 创建一个日志中间件
func loggingMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		start := time.Now()
		log.Printf("Started %s %s", r.Method, r.URL.Path)
		log.Printf("Request headers: %+v", r.Header)

		next.ServeHTTP(w, r)

		log.Printf("Completed %s %s in %v", r.Method, r.URL.Path, time.Since(start))
	})
}

func main() {
	// 初始化日志系统
	xlogs.Init(xlogs.DefaultConfig())

	// 设置日志格式
	log.SetFlags(log.Ldate | log.Ltime | log.Lmicroseconds | log.Lshortfile)
	log.Printf("Starting auth client service...")

	// 加载配置
	serverConfig, authConfig, err := LoadConfig("config.json")
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 创建用户服务实例
	userService := NewUserService(authConfig)

	// 创建路由
	mux := http.NewServeMux()

	// 注册健康检查路由（不需要认证）
	mux.Handle("/health", loggingMiddleware(http.HandlerFunc(userService.handleHealthCheck)))

	// 注册需要认证的路由
	// 使用认证中间件包装处理函数
	mux.Handle("/api/user/info", loggingMiddleware(
		userService.authClient.AuthMiddleware(
			http.HandlerFunc(userService.handleUserInfo),
		),
	))

	// 创建 HTTP 服务器
	server := &http.Server{
		Addr:         serverConfig.Port,
		Handler:      mux,
		ReadTimeout:  serverConfig.ReadTimeout,
		WriteTimeout: serverConfig.WriteTimeout,
		IdleTimeout:  serverConfig.IdleTimeout,
	}

	// 启动服务器
	log.Printf("Starting user service on %s", server.Addr)
	log.Printf("Configuration: Server Port: %s, Auth Server URL: %s",
		serverConfig.Port, authConfig.ServerURL)

	if err := server.ListenAndServe(); err != nil {
		log.Fatalf("Server failed: %v", err)
	}
}
