# Auth服务模拟测试

本目录包含用于模拟ZTNA Auth服务的测试工具，方便开发和测试。

## 文件说明

- `auth_mock_server.go` - 模拟Auth服务的Go实现
- `test_mock_auth.sh` - 测试脚本，用于测试模拟服务的各项功能
- `login_test.sh` - 测试实际Auth服务的脚本

## 环境要求

- Go 1.18+
- jq (用于JSON处理)
- curl

## 使用方法

### 1. 启动模拟服务

```bash
cd test
go run auth_mock_server.go
```

服务将在 http://localhost:8081 上运行，以避免与实际的Auth服务（8080端口）冲突。

### 2. 运行测试脚本

在另一个终端窗口中：

```bash
cd test
chmod +x test_mock_auth.sh
./test_mock_auth.sh
```

## 支持的功能

模拟服务实现了以下功能：

1. **用户认证**
   - 基本登录 (`/api/login`)
   - OAuth 2.0 密码模式 (`/oauth/token`)
   - 令牌刷新 (`/api/refresh` 和 `/oauth/token` 的 refresh_token 模式)
   - 登出 (`/api/logout`)

2. **API保护**
   - 访问受保护资源 (`/api/protected`)
   - JWT验证

3. **测试用户**
   - admin/admin123
   - user/password

## API 接口

### 登录

```
POST /api/login
Content-Type: application/json

{
  "username": "admin",
  "password": "admin123"
}
```

### OAuth 2.0 密码模式

```
POST /oauth/token
Content-Type: application/x-www-form-urlencoded

grant_type=password&username=admin&password=admin123&client_id=test_client&client_secret=test_secret
```

### 刷新令牌

```
POST /api/refresh
Content-Type: application/json

{
  "refresh_token": "YOUR_REFRESH_TOKEN"
}
```

### OAuth 2.0 刷新令牌

```
POST /oauth/token
Content-Type: application/x-www-form-urlencoded

grant_type=refresh_token&refresh_token=YOUR_REFRESH_TOKEN&client_id=test_client&client_secret=test_secret
```

### 访问受保护资源

```
GET /api/protected
Authorization: Bearer YOUR_ACCESS_TOKEN
```

### 登出

```
POST /api/logout
Authorization: Bearer YOUR_ACCESS_TOKEN
```

## 与实际服务的差异

模拟服务简化了一些功能，主要区别包括：

1. 使用内存存储而不是Redis
2. 简化的密码验证（明文比较而非加密比较）
3. 简化的令牌生成（使用时间戳而非UUID或其他标准格式）
4. 不支持所有OAuth 2.0流程（仅支持password和refresh_token）
5. 不支持第三方登录（如微信、钉钉）

这些简化旨在提供一个轻量级的测试环境，适用于大多数开发和测试场景。 