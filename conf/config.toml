[service]
name = "ztna_auth_service"
mode = "debug"
version = "1.0.0"
auth_addr = ":8080"

[redis_default]
switch = false
connection_mode = 0
addr = ["localhost:6379"]
password = ""
db = 0

[logging]
level = "debug"
format = "json"
output = "stdout"

# JWT配置
[jwt]
secret_key = "your-secret-key-here"
access_expiry = "24h"    # 访问令牌过期时间
refresh_expiry = "720h"  # 刷新令牌过期时间（30天）
issuer = "ztna_auth_service"

# 测试用户配置
[test_users]
[[test_users.user]]
user_id = "user1"
username = "admin"
password = "admin123"
email = "<EMAIL>"
phone = "13800138000"
org_name = "测试组织"

[[test_users.user]]
user_id = "user2"
username = "user"
password = "password"
email = "<EMAIL>"
phone = "13900139000"
org_name = "测试组织" 